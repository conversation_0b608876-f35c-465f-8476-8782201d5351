<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="SDK Doxi Run" type="Application" factoryName="Application">
    <extension name="coverage" enabled="false" merge="false" sample_coverage="true" runner="idea" />
    <option name="MAIN_CLASS_NAME" value="com.sencha.command.Sencha" />
    <option name="VM_PARAMETERS" value="" />
    <option name="PROGRAM_PARAMETERS" value="doxi build -p classic-project.doxi.json all" />
    <option name="WORKING_DIRECTORY" value="file://$PROJECT_DIR$/../SDK6/docs" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
    <option name="ALTERNATIVE_JRE_PATH" />
    <option name="ENABLE_SWING_INSPECTOR" value="false" />
    <option name="ENV_VARIABLES" />
    <option name="PASS_PARENT_ENVS" value="true" />
    <module name="sencha-test-harness" />
    <envs />
    <method>
      <option name="Make" enabled="false" />
      <option name="AntTarget" enabled="true" antfile="file://$PROJECT_DIR$/build.xml" target="build" />
    </method>
  </configuration>
</component>