<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/modules/doxi/Doxi.iml" filepath="$PROJECT_DIR$/modules/doxi/Doxi.iml" />
      <module fileurl="file://$PROJECT_DIR$/cmd-packager/cmd-packager.iml" filepath="$PROJECT_DIR$/cmd-packager/cmd-packager.iml" />
      <module fileurl="file://$PROJECT_DIR$/modules/fashion/fashion.iml" filepath="$PROJECT_DIR$/modules/fashion/fashion.iml" />
      <module fileurl="file://$PROJECT_DIR$/sencha-command/sencha-command.iml" filepath="$PROJECT_DIR$/sencha-command/sencha-command.iml" />
      <module fileurl="file://$PROJECT_DIR$/sencha-command-compass/sencha-command-compass.iml" filepath="$PROJECT_DIR$/sencha-command-compass/sencha-command-compass.iml" />
      <module fileurl="file://$PROJECT_DIR$/sencha-command-service/sencha-command-service.iml" filepath="$PROJECT_DIR$/sencha-command-service/sencha-command-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/sencha-command-test/sencha-command-test.iml" filepath="$PROJECT_DIR$/sencha-command-test/sencha-command-test.iml" />
      <module fileurl="file://$PROJECT_DIR$/sencha-fashion/sencha-fashion.iml" filepath="$PROJECT_DIR$/sencha-fashion/sencha-fashion.iml" />
      <module fileurl="file://$PROJECT_DIR$/sencha-test-harness/sencha-test-harness.iml" filepath="$PROJECT_DIR$/sencha-test-harness/sencha-test-harness.iml" />
    </modules>
  </component>
</project>
