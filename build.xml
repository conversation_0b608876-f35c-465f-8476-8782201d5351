<!--
NOTE - DO NOT STORE PASSWORDS, ACCESS TOKENS OR KEYS OF ANY KIND IN THIS FILE
-->
<project name="SenchaCmd" default=".help">
    <import file="build-util.xml"/>
    <import file="artifact-setup.xml"/>
    <import file="integration-test.xml"/>
    
    <!--
    Initialize just the properties.
    -->
    <target name="init" depends="init-util,init-artifact-setup">
        <condition property="use.shell" value="true">
            <os family="unix"/>
        </condition>
    </target>

    <!--
    Fully initializes properties and scripts tasks.
    -->
    <target name="init-all" depends="init">
        <!-- Ensure the build and dist dirs exist -->
        <mkdir dir="${build.dir}"/>
        <mkdir dir="${dist.dir}"/>

        <!--
        This is the guts of x-git-current-hash but since that is provided by
        sencha.jar and that is what we are *about* to build, we cannot use it
        from there.
        
            <x-git-current-hash property="git.current.hash"/>
        -->
        <property name="x-git.exe" value="git"/>
        <exec executable="${x-git.exe}" outputproperty="git.current.hash"
                                        failifexecutionfails="false">
            <arg value="log"/>
            <arg value="-1"/>
            <arg value="--format=%H"/>
            <env key="PATH" path="${x-env.PATH}"/>
        </exec>
    </target>

    <target name="init-sencha" depends="init-all">
        <x-load-sencha cmddir="${sencha.dist.dir}"/>
    </target>

    <!-- ****************************************************************** -->

    <target name="release-notes" depends="init-sencha" unless="skip.releasenotes">
        <x-make-url property="jira.release-notes.query"
                    scheme="https"
                    host="${jira.host}"
                    path="${jira.search.base}">
            <query param="tempMax" value="${jira.max.results}"/>
        </x-make-url>

        <x-jira-query url="${jira.release-notes.query}&amp;jqlQuery="
                      srcfile="${sencha.dir}/docs/release-notes.html"
                      outfile="${sencha.dist.dir}/release-notes.html"
                      forumprefix="${sencha.dist.dir}/release-notes-"
                      user="${jira.user.name}"
                      pswd="${jira.user.pswd}"
                      failonerror="true"/>

        <!-- Remove external dependencies: -->
        <x-include srcfile="${sencha.dist.dir}/release-notes.html"/>

        <replace file="${sencha.dist.dir}/release-notes.html">
            <replacefilter token="@@CMD_VERSION@@" value="${tools.version}"/>
            <replacefilter token="@@BUILD_DATE@@" value="${tstamp.pretty}"/>
        </replace>

        <copy file="${sencha.dir}/docs/logo-sencha-sm.png"
              tofile="${sencha.dist.dir}/logo-sencha-sm.png"/>
    </target>

    <!-- ****************************************************************** -->

    <target name=".props" depends="init-all"
            description="Displays a list of all defined properties for this build">
        <echo message="All currently defined properties:" />
        <echoproperties/>
    </target>

    <target name=".help" depends="init-all"
            description="Displays help on properties and options for this build">
        <exec executable="ant" vmlauncher="${use.shell}">
            <arg value="-f"/>
            <arg value="${ant.file}"/>
            <arg value="-p"/>
        </exec>
        <echo><![CDATA[This is the main build script for Sencha Cmd.

${x-all-targets}

There are several properties that can be used to control the build script.

These properties can be defined to control various aspects of the build:

 * build.dir        The directory where build output should be placed
 * dist.dir         The directory where build artifacts should be placed
 * changelog.dir    The directory where previous changelogs are stored.
 * jira.max.results The maximum number of tickets to return from a Jira query.
 * jira.user.name   The Jira username to use for ticket queries
 * jira.user.pswd   The password for the jira.user.name user
 * zip.name         The base name of generated zip files ({$zip.name})

The following properties can be defined to disable certain steps or targets.
These are mostly useful for testing and changing the build script to avoid
lengthy operations:

 * tools.version.number  The build number of sencha command, eg. 3.1.0
 * tools.version.label   The status of the build : Alpha, Beta, etc.
 * maven.skipTests       Skips running unit tests under build target

These options can be stored in a local.properties file in this folder or in the
SDK root folder. The contents of local.properties in this folder take priority
over those in the SDK root folder.

Alternatively, these can be supplied on the command line. For example:

    ant -Dopt.dryrun=1 -Dopt.nocss=1 build

To see all currently defined properties, do this:

    ant .props
        ]]></echo>
    </target>

    <macrodef name="x-build-subproject">
        <attribute name="project"/>
        <attribute name="target"/>
        <sequential>
            <ant dir="@{project}"
                 inheritall="false"
                 inheritrefs="false"
                 usenativebasedir="true"
                 target="@{target}"/>
        </sequential>
    </macrodef>

    <macrodef name="x-build-projects">
        <attribute name="target"/>
        <sequential>
<!--            <x-build-subproject project="sencha-command" target="@{target}"/>
            <x-build-subproject project="sencha-command-service" target="@{target}"/>
            <x-build-subproject project="sencha-command-compass" target="@{target}"/>
            <x-build-subproject project="sencha-command-test" target="@{target}"/>-->
            <x-build-subproject project="sencha-test-harness" target="@{target}"/>
        </sequential>
    </macrodef>

    <target name="clean" depends="init-all">
        <delete dir="${build.dir}"/>
        <x-build-projects target="clean"/>
        <delete dir="${sencha.dist.dir}"/>
        <delete dir="${sencha.service.dist.dir}"/>
        <delete dir="${sencha.compass.dist.dir}"/>
        <delete dir="${sencha.test.dist.dir}"/>
        <delete dir="${sencha.test.harness.dist.dir}"/>
    </target>

    <target name="build" depends="init-all">
        <x-build-projects target="jar"/>
        <antcall target="stamp-cmd-version"/>
        <if>
            <not>
                <os family="windows"/>
            </not>
            <then>
                <shellscript shell="sh">
                    chmod +x ${sencha.test.harness.dist.dir}/lib/Sencha/Cmd/sencha
                </shellscript>
            </then>
        </if>
    </target>

    <target name="build-local" depends="build">
        <copy file="${basedir}/sencha-test-harness/dist/lib/Sencha/Cmd/sencha.exe"
              tofile="${basedir}/sencha-test-harness/dist/lib/Sencha/Cmd/sencha-dev.exe"/>
    </target>

    <target name="sencha-command-tests" depends="init-all">
        <x-build-subproject project="sencha-command" target="external-test-run"/>
    </target>

    <target name="sencha-fashion-tests" depends="init-all">
        <x-build-subproject project="sencha-fashion" target="external-test-run"/>
    </target>

    <target name="sencha-service-tests" depends="init-all">
        <x-build-subproject project="sencha-command-service" target="external-test-run"/>
    </target>

    <target name="sencha-test-harness-tests" depends="init-all">
        <x-build-subproject project="sencha-test-harness" target="external-test-run"/>
    </target>

    <target name="core-test" depends="sencha-command-tests,sencha-fashion-tests,sencha-service-tests"/>
    <target name="test" depends="core-test,sencha-test-harness-tests"/>

    <target name="get-configs" depends="init-all">

        <get src="${teamcity.ext.config.file}"
             dest="${build.dir}/ext.sencha.cfg"
             username="${teamcity.username}"
             password="${teamcity.password}"
             ignoreerrors="true"/>

        <get src="${teamcity.touch.config.file}"
             dest="${build.dir}/touch.sencha.cfg"
             username="${teamcity.username}"
             password="${teamcity.password}"
             ignoreerrors="true"/>

    </target>

    <target name="check-ext-config" depends="get-configs">
        <available file="${build.cache.dir}/ext.sencha.cfg" property="ext.hasconfig"/>
    </target>

    <target name="stamp-ext-version"
            depends="check-ext-config"
            if="ext.hasconfig">

        <property name="ext.config.file" location="${build.cache.dir}/ext.sencha.cfg"/>
        <property file="${ext.config.file}" prefix="ext."/>
        <replace file="${dist.dir}/all/plugins/ext/current/sencha.cfg"
                 token="cmd.framework.version=99.99"
                 value="cmd.framework.version=${ext.framework.version}"/>

    </target>

    <target name="check-touch-config" depends="get-configs">
        <available file="${build.cache.dir}/touch.sencha.cfg" property="touch.hasconfig"/>
    </target>

    <target name="stamp-touch-version"
            depends="check-touch-config"
            if="touch.hasconfig">

        <property name="touch.config.file" location="${build.cache.dir}/touch.sencha.cfg"/>
        <property file="${touch.config.file}" prefix="touch."/>
        <replace file="${dist.dir}/all/plugins/touch/current/sencha.cfg"
                 token="cmd.framework.version=99.99"
                 value="cmd.framework.version=${touch.framework.version}"/>

    </target>

    <target name="stamp-versions"
            depends="stamp-ext-version,stamp-touch-version"/>

    <macrodef name="codesign-binaries">
        <attribute name="exec-name" default="" />
        <attribute name="exec-path" />
        <attribute name="target" />
        <attribute name="url" />
        <sequential>
            <exec executable="curl" failonerror="true" vmlauncher="${use.shell}">
                <arg value="-F" />
                <arg value="executable_name=@{exec-name}" />
                <arg value="-F" />
                <arg value="binary=@@@{exec-path}" />
                <arg value="-o" />
                <arg value="@{target}" />
                <arg value="@{url}" />
            </exec>

            <local name="codesign.file.size" />
            <length file="@{target}" property="codesign.file.size" />

            <!-- After the sign appears to be complete, verify the filesize
                  to prevent the scenario where the artifacts appear to be ready
                  but are actually text files with the error output form the signer
            -->
            <if>
                <!-- If the result file is less than 10K it is safe to assume it contains the error message -->
                <scriptcondition language="javascript"><![CDATA[
                    self.setValue( 
                        Number(project.getProperty("codesign.file.size")) < 10240
                    );
                ]]></scriptcondition>
                <then>
                    <local name="error.message" />  
                    <loadfile property="error.message" srcFile="@{target}"/>
                    <fail message="There was an error while signing '@{target}': ${error.message}" />
                </then>
            </if>


        </sequential>
    </macrodef>

    <target name="stamp-cmd-version" depends="init">
        <!-- stamp the version into the config file -->
        <echo>Applying version ${tools.version} to ${sencha.dist.dir}/sencha.cfg</echo>
        <replaceregexp file="${sencha.dist.dir}/sencha.cfg"
                       match="cmd.version=(\d{1,5}\.)+\d{1,5}"
                       replace="cmd.version=${tools.version}"
                       byline="true"
                       flags="g"/>
        <replaceregexp file="${sencha.test.harness.dist.dir}/lib/Sencha/Cmd/sencha.cfg"
                       match="cmd.version=(\d{1,5}\.)+\d{1,5}"
                       replace="cmd.version=${tools.version}"
                       byline="true"
                       flags="g"/>
    </target>

    <target name="copy-build" depends="stamp-cmd-version,release-notes">
        <!--generate the version.properties file for deployments to cdn-->
<echo file="${sencha.dist.dir}/version.properties"><![CDATA[# Build date: ${tstamp.datetime}
version.major=${version.major}
version.minor=${version.minor}
version.patch=${version.patch}
version.build=${version.build}
version.full=${tools.version}
version.git.hash=${git.current.hash}
]]></echo>

        <delete dir="${dist.dir}/.." />

        <copy todir="${dist.dir}/all">
            <fileset dir="${sencha.dist.dir}"
                     includes="templates/**/*,
                               ant/**/*,
                               lib/**/*.jar,
                               sencha.cfg,
                               plugin.xml,
                               plugins/**/*,
                               extensions/**/*,
                               *.js,
                               shell-wrapper.sh,
                               release-notes.html,
                               *.png,
                               sencha.jar,
                               unicode-escapes.json,
                               legacy/**/*,
                               js/**/*"/>
        </copy>
        
        <copy todir="${dist.dir}/all/extensions/sencha-compass">
            <fileset dir="${sencha.compass.dist.dir}"
                     includes="lib/**/*.jar,
                               *.jar,
                               .sencha/**/*,
                               package.json,
                               gems/**/*"/>
        </copy>

        <copy todir="${dist.dir}/all/extensions/sencha-service">
            <fileset dir="${sencha.service.dist.dir}"
                     includes="lib/**/*.jar,
                               *.jar,
                               shell-wrapper.sh,
                               .sencha/**/*,
                               package.json"/>
        </copy>

        <copy todir="${dist.dir}/all/extensions/sencha-fashion">
            <fileset dir="${sencha.fashion.dist.dir}"
                     includes="lib/**/*,
                               fashion/**/*,
                               *.jar,
                               .sencha/**/*,
                               *,
                               package.json"/>
        </copy>

        <if>
            <isset property="package.test"/>
            <then>
                <copy todir="${dist.dir}/all/extensions/sencha-test">
                    <fileset dir="${sencha.test.dist.dir}"
                             includes="lib/**/*.jar,
                                       *.jar,
                                       .sencha/**/*,
                                       package.json"/>
                </copy>
            </then>
        </if>
        
        <copy todir="${dist.dir}/win"
              file="${sencha.dist.dir}/sencha.exe"/>

        <copy tofile="${dist.dir}/win/sencha-${tools.version}.exe"
              file="${sencha.dist.dir}/sencha.exe"/>

        <!-- include sencha bash script for unix terms on windows -->
        <copy todir="${dist.dir}/win"
              file="${sencha.dist.dir}/sencha"/>

        <copy todir="${dist.dir}/mac"
              file="${sencha.dist.dir}/sencha"/>

        <copy tofile="${dist.dir}/mac/sencha-${tools.version}"
              file="${sencha.dist.dir}/sencha"/>

        <copy todir="${dist.dir}/linux-x86"
              file="${sencha.dist.dir}/sencha"/>

        <copy tofile="${dist.dir}/linux-x86/sencha-${tools.version}"
              file="${sencha.dist.dir}/sencha"/>

        <copy todir="${dist.dir}/linux-x64"
              file="${sencha.dist.dir}/sencha"/>

        <copy tofile="${dist.dir}/linux-x64/sencha-${tools.version}"
              file="${sencha.dist.dir}/sencha"/>

        <antcall target="stamp-versions"/>

        <copy todir="${dist.dir}/switch"
              file="${sencha.dist.dir}/sencha.exe"/>

        <copy tofile="${dist.dir}/switch/sencha"
              file="${sencha.dist.dir}/sencha-switch"/>

        <echo file="${dist.dir}/switch/version.properties"><![CDATA[version.full=${tools.version}]]></echo>

        <if>
            <isset property="codesign.url"/>
            <then>
                <codesign-binaries exec-path="${dist.dir}/all/sencha.jar" target="${dist.dir}/all/sencha.jar" url="${codesign.url}"/>
                <codesign-binaries exec-path="${dist.dir}/win/sencha.exe" target="${dist.dir}/win/sencha.exe" url="${codesign.url}"/>

                <codesign-binaries
                    exec-path="${dist.dir}/all/extensions/sencha-compass/sencha-compass.jar" 
                    target="${dist.dir}/all/extensions/sencha-compass/sencha-compass.jar" 
                    url="${codesign.url}"/>
                
                <codesign-binaries
                    exec-path="${dist.dir}/all/extensions/sencha-service/sencha-service.jar" 
                    target="${dist.dir}/all/extensions/sencha-service/sencha-service.jar" 
                    url="${codesign.url}"/>

                <if>
                    <isset property="package.test"/>
                    <then>
                    <codesign-binaries 
                        exec-path="${dist.dir}/all/extensions/sencha-test/sencha-test.jar" 
                        target="${dist.dir}/all/extensions/sencha-test/sencha-test.jar" 
                        url="${codesign.url}"/>
                    </then>
                </if>
                
            </then>
        </if>
    </target>

    <macrodef name="x-stage-binaries">
        <attribute name="distdir"/>
        <attribute name="cachedir"/>
        <sequential>
            <copy todir="@{distdir}">
                <fileset 
                    dir="@{cachedir}"
                    includes="phantomjs/**/*,
                              node/**/*,
                              vcdiff/**/*"/>
            </copy>
        </sequential>
    </macrodef>

    <!--Initialize the dependency paths for the various platforms-->
    <target name="init-workspace-linux-x86" 
            depends="init-all,
                     init-artifact-setup,
                     get-phantomjs,
                     get-node,
                     get-vcdiff">
        
        <x-stage-binaries
            distdir="${dist.dir}/linux-x86/bin/linux"
            cachedir="${build.cache.lin32.dir}"/>
    </target>

    <target name="init-workspace-linux-x64" 
            depends="init-all,
                     init-artifact-setup,
                     get-phantomjs,
                     get-node,
                     get-vcdiff">
        
        <x-stage-binaries
            distdir="${dist.dir}/linux-x64/bin/linux-x64"
            cachedir="${build.cache.lin64.dir}"/>
    </target>

    <target name="init-workspace-mac" 
            depends="init-all,
                     init-artifact-setup,
                     get-phantomjs,
                     get-node,
                     get-vcdiff">
        
        <x-stage-binaries
            distdir="${dist.dir}/mac/bin/osx"
            cachedir="${build.cache.mac.dir}"/>
    </target>

    <target name="init-workspace-win" 
            depends="init-all,
                     init-artifact-setup,
                     get-phantomjs,
                     get-node,
                     get-vcdiff">
        
        <x-stage-binaries
            distdir="${dist.dir}/win/bin/windows"
            cachedir="${build.cache.win.dir}"/>
    </target>

    <target name="init-workspace" 
            depends="init-all,
                     init-workspace-linux-x86,
                     init-workspace-linux-x64,
                     init-workspace-mac,
                     init-workspace-win">
    </target>

    <macrodef name="x-extract-app-bundle">
        <attribute name="filename" />
        <attribute name="dir" />
        <sequential>
            <exec executable="./dmg-extractor">
                <arg value="@{filename}" />
                <arg value="@{dir}" />
            </exec>
        </sequential>
    </macrodef>

    <target name="generate-installers" depends="init-workspace">
        <sequential>
            <exec executable="install4jc" >
                <arg value="sencha-cmd.install4j" />
                <arg value="--mac-keystore-password=Sencha@123" />
                <arg value="--apple-id-password=rmvp-thjw-tcyg-dpqr" />
                <arg value="-s" />
                <arg value="-r" />
                <arg value="${tools.version}" />
            </exec>

            <x-extract-app-bundle filename="SenchaCmd-${tools.version}-osx" dir=".build" />
            <x-extract-app-bundle filename="SenchaCmd-${tools.version}-osx-arm" dir=".build" />
            <x-extract-app-bundle filename="SenchaCmd-${tools.version}-osx-no_jre" dir=".build" />

            <if>
                <isset property="codesign.url.osx"/>
                <then>
                    <delete file="${dist.dir}/../SenchaCmd-${tools.version}-osx.app/Contents/vmoptions.txt" />
                    <tar
                        compression="gzip"
                        destfile="${dist.dir}/../SenchaCmd-${tools.version}-osx.app.tar.gz">
                        <tarfileset
                            dir="${dist.dir}/../SenchaCmd-${tools.version}-osx.app"
                            prefix="SenchaCmd-${tools.version}-osx.app"
                            filemode="777" />
                    </tar>
                    <delete dir="${dist.dir}/../SenchaCmd-${tools.version}-osx.app" />
                    <codesign-binaries
                        exec-name="SenchaCmd-${tools.version}-osx.app"
                        exec-path="${dist.dir}/../SenchaCmd-${tools.version}-osx.app.tar.gz"
                        target="${dist.dir}/../SenchaCmd-${tools.version}-osx.app.tar.gz"
                        url="${codesign.url.osx}"/>
                    <untar
                        compression="gzip"
                        src="${dist.dir}/../SenchaCmd-${tools.version}-osx.app.tar.gz"
                        dest="${dist.dir}/../"/>
                    <delete file="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app/Contents/vmoptions.txt" />
                    <tar
                        compression="gzip"
                        destfile="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app.tar.gz">
                        <tarfileset
                            dir="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app"
                            prefix="SenchaCmd-${tools.version}-osx-no_jre.app"
                            filemode="777" />
                    </tar>
                    <delete dir="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app" />
                    <codesign-binaries
                        exec-name="SenchaCmd-${tools.version}-osx-no_jre.app"
                        exec-path="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app.tar.gz"
                        target="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app.tar.gz"
                        url="${codesign.url.osx}"/>
                    <untar
                        compression="gzip"
                        src="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app.tar.gz"
                        dest="${dist.dir}/../"/>
                </then>
            </if>

        </sequential>
    </target>

    <target name="external-codesign" depends="init">
        <if>
            <and>
                <isset property="codesign.url"/>
                <isset property="binaryfile"/>
            </and>
            <then>
                <codesign-binaries exec-path="${binaryfile}" target="${binaryfile}" url="${codesign.url}"/>
            </then>
        </if>
    </target>

    <target name="generate-zips" depends="init-workspace">
        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-osx.zip">
            <fileset dir="${dist.dir}/all"/>
            <zipfileset dir="${dist.dir}/mac" filemode="755" />
        </zip>

        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-osx.app.zip">
            <zipfileset  dir="${dist.dir}/../SenchaCmd-${tools.version}-osx.app"
                         prefix="SenchaCmd-${tools.version}-osx.app"
                         filemode="755"/>
        </zip>

        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-osx-arm.zip">
            <zipfileset  dir="${dist.dir}/../SenchaCmd-${tools.version}-osx-arm.app"
                         prefix="SenchaCmd-${tools.version}-osx.app"
                         filemode="755"/>
        </zip>

        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app.zip">
            <zipfileset dir="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app"
                        prefix="SenchaCmd-${tools.version}-osx-no_jre.app"
                        filemode="755" />
        </zip>

        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-windows-32bit.zip">
            <zipfileset dir="${dist.dir}/../" includes="SenchaCmd-${tools.version}-windows-32bit.exe" filemode="755"/>
        </zip>
        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-windows-64bit.zip">
            <zipfileset dir="${dist.dir}/../" includes="SenchaCmd-${tools.version}-windows-64bit.exe" filemode="755"/>
        </zip>
        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-windows-no_jre.zip">
            <zipfileset dir="${dist.dir}/../" includes="SenchaCmd-${tools.version}-windows-no_jre.exe" filemode="755"/>
        </zip>
        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-windows.zip">
            <fileset dir="${dist.dir}/all"/>
            <zipfileset dir="${dist.dir}/win" filemode="755" />
        </zip>
        <!-- Run sed command to modify the installer.sh file text and at most 99  -->
        <exec executable="perl">
            <arg value="-i"/>
            <arg value="-pe"/>
            <arg value="s/and at most 99/or higher/" />
            <arg value="${dist.dir}/../SenchaCmd-${tools.version}-linux-i386.sh" />
        </exec>

        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-linux-i386.sh.zip">
            <zipfileset dir="${dist.dir}/../" includes="SenchaCmd-${tools.version}-linux-i386.sh" filemode="755"/>
        </zip>
        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-linux-i386.zip">
            <fileset dir="${dist.dir}/all"/>
            <zipfileset dir="${dist.dir}/linux-x86" filemode="755" />
        </zip>
        <!-- Run sed command to modify the installer.sh file text and at most 99 -->
        <exec executable="perl">
            <arg value="-i"/>
            <arg value="-pe"/>
            <arg value="s/and at most 99/or higher/" />
            <arg value="${dist.dir}/../SenchaCmd-${tools.version}-linux-amd64.sh" />
        </exec>
        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-linux-amd64.sh.zip">
            <zipfileset dir="${dist.dir}/../" includes="SenchaCmd-${tools.version}-linux-amd64.sh" filemode="755"/>
        </zip>
        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-linux-amd64.zip">
            <fileset dir="${dist.dir}/all"/>
            <zipfileset dir="${dist.dir}/linux-x64" filemode="755" />
        </zip>

        <zip destfile="${dist.dir}/../SenchaCmd-${tools.version}-universal.zip">
            <fileset dir="${dist.dir}/all"/>
            <zipfileset dir="${dist.dir}/mac" filemode="755" />
            <zipfileset dir="${dist.dir}/win" filemode="755" />
            <zipfileset dir="${dist.dir}/linux-x64" filemode="755" />
            <zipfileset dir="${dist.dir}/linux-x86" filemode="755" />
        </zip>
    </target>

    <target name="cleanup-installers" depends="init-all">
        <!-- remove intermediate installer files from installbuilder dir -->
        <delete dir="${dist.dir}"/>
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-osx.dmg" />
        <delete dir="${dist.dir}/../SenchaCmd-${tools.version}-osx.app" />
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-osx.app.tar.gz" />
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.dmg" />
        <delete dir="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app" />
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-osx-no_jre.app.tar.gz" />
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-windows-32bit.exe" />
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-windows-64bit.exe" />
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-windows-no_jre.exe" />
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-linux-i386.sh" />
        <delete file="${dist.dir}/../SenchaCmd-${tools.version}-linux-amd64.sh" />
        <delete file="sencha-cmd.install4j.tmp" />
    </target>
    
    <target name="create-installers" 
            depends="init-all,
                     copy-build,
                     init-workspace,
                     generate-installers,
                     generate-zips,
                     cleanup-installers">
    </target>

    <target name="deploy-init">
        <property file="sencha.cfg"/>
        <property name="cdn.server" value="ftp.cachefly.com"/>
        <property name="cdn.folder" value="cmd"/>
    </target>
    
    <target name="deploy-prep" depends="deploy-init,init">
        <echo>Deploy to ftp://${cdn.username}@${cdn.server}/${cdn.folder}/${cmd.version}</echo>
    </target>
    
    <target name="deploy" depends="deploy-prep">
        <ftp 
            server="${cdn.server}" 
            action="mkdir" 
            remotedir="${cdn.folder}/${cmd.version}"
            userid="${cdn.username}"
            password="${cdn.password}" 
            passive="true"/>

        <ftp 
            server="${cdn.server}" 
            remotedir="${cdn.folder}/${cmd.version}/jre"
            userid="${cdn.username}"
            password="${cdn.password}" 
            passive="true">
            
            <fileset dir="${cdn.staging.dir}/jre" />
        </ftp>

        <ftp
                server="${cdn.server}"
                remotedir="${cdn.folder}/${cmd.version}/no-jre"
                userid="${cdn.username}"
                password="${cdn.password}"
                passive="true">

            <fileset dir="${cdn.staging.dir}/no-jre" />
        </ftp>

        <ftp
                server="${cdn.server}"
                remotedir="${cdn.folder}/${cmd.version}/zip"
                userid="${cdn.username}"
                password="${cdn.password}"
                passive="true">

            <fileset dir="${cdn.staging.dir}/zip" />
        </ftp>

        <ftp
                server="${cdn.server}"
                remotedir="${cdn.folder}/${cmd.version}"
                userid="${cdn.username}"
                password="${cdn.password}"
                passive="true">

            <fileset
                dir="${cdn.staging.dir}"
                includes="release-notes.zip"/>
        </ftp>
        
        <ftp 
            server="${cdn.server}" 
            remotedir="${cdn.folder}"
            userid="${cdn.username}"
            password="${cdn.password}" 
            passive="true">
            
            <fileset 
                dir="${base.dir}" 
                includes="version.properties"/>
        </ftp>
    </target>
    
    <target name="deploy-qa" depends="init, init-sencha">
        <local name="deploy.dir"/>
        <property name="deploy.dir" value="./build/deploy"/>

        <!-- make sure it is clean -->
        <delete dir="${deploy.dir}"/>
        <mkdir dir="${deploy.dir}/${tools.version.number}"/>
        <mkdir dir="${deploy.dir}/${tools.version.number}/jre"/>
        <mkdir dir="${deploy.dir}/${tools.version.number}/no-jre"/>
        <mkdir dir="${deploy.dir}/${tools.version.number}/zip"/>

        <copy todir="${deploy.dir}/${tools.version.number}/jre">
            <fileset dir="${build.dir}/jre" />
        </copy>

        <copy todir="${deploy.dir}/${tools.version.number}/no-jre">
            <fileset dir="${build.dir}/no-jre" />
        </copy>

        <copy todir="${deploy.dir}/${tools.version.number}/zip">
            <fileset dir="${build.dir}/zip" />
        </copy>

        <copy todir="${deploy.dir}/${tools.version.number}">
            <fileset
                dir="${build.dir}"
                includes="release-notes.zip,README.md" />
        </copy>

        <copy todir="${deploy.dir}">
            <fileset dir="${sencha.dist.dir}" includes="version.properties"/>
        </copy>
        
        <x-scp 
            keyfile="${deploy.qa.ssh.key}"
            localfile="${deploy.dir}/*"
            remotefile="${deploy.qa.dest.host}:${deploy.qa.dest.path}"/>

        <delete dir="${deploy.dir}"/>
    </target>
    
    <target name="build-deploy"
            depends="init"
            description="Builds and Deploys Sencha Cmd over the current system install">
        
        <antcall target="build" inheritall="true" inheritrefs="true">
            <param name="maven.skipTests" value="true"/>
        </antcall>
        
        <local name="sencha.which.file"/>
        
        <property name="sencha.which.file" 
                  location="cmd.which.version.properties"/>
        
        <exec executable="sencha" vmlauncher="${use.shell}">
            <arg value="which"/>
            <arg value="-p=sencha.cmd.dir"/>
            <arg value="-o=${sencha.which.file}"/>
        </exec>
        
        <if>
            <available file="${sencha.which.file}"/>
            <then>
                <property file="${sencha.which.file}" prefix="which"/>
                <delete dir="${which.sencha.cmd.dir}"
                        includeemptydirs="true"
                        followsymlinks="false"
                        includes="ant/**/*,
                                  bin/**/*,
                                  templates/**/*,
                                  plugins/**/*,
                                  legacy/**/*,
                                  extensions/**/*,
                                  phantomjs/**/*,
                                  vcdiff/**/*"/>

                <copy todir="${which.sencha.cmd.dir}" overwrite="true">
                    <fileset dir="${sencha.test.harness.dist.dir}/lib/Sencha/Cmd" includes="**/*"/>
                </copy>

                <!--include the updated sencha.cfg with the version stamp-->
                <copy todir="${which.sencha.cmd.dir}" overwrite="true">
                    <fileset dir="${sencha.dist.dir}" includes="sencha.cfg"/>
                </copy>

                <if>
                    <equals arg1="${deploy.sencha.test}" arg2="true"/>
                    <then>
                        <copy todir="${which.sencha.cmd.dir}/extensions/cmd-test" overwrite="true">
                            <fileset dir="${sencha.test.dist.dir}" includes="**/*"/>
                        </copy>
                        <exec executable="sencha" vmlauncher="${use.shell}">
                            <arg value="package"/>
                            <arg value="install"/>
                            <arg value="cmd-webdriver"/>
                        </exec>
                    </then>
                </if>
                
                <delete file="${sencha.which.file}"/>
            </then>
        </if>
    </target>

    <target name="build-all" depends="build">
    </target>

    
    <target name="post-build" depends="init">
        <delete dir="${sencha.test.harness.dist.dir}/lib/Sencha/repo"
                includeemptydirs="true"/>
        <delete dir="${sencha.test.harness.dist.dir}/test-apps/AutoDepTestApp/build"
                includeemptydirs="true"/>
    </target>
    
</project>
