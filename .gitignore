.DS_Store
.sass-cache
.sass-cache/*
*.komodoproject
.tmp*
/nbproject

# as per : http://devnet.jetbrains.com/docs/DOC-1186;jsessionid=0108BDBD5632E663D2C152B6C4A8E92A
# only ignore .idea/workspace.xml and .idea/tasks.xml, as those contain user specific info
/.idea/workspace.xml
/.idea/tasks.xml

# intellij idea's default build dir
/out/

extjs/deploy/
touch/deploy/
platform/deploy/
charts/deploy/
/java/
/WEB-INF/
.project
.classpath
/build/node_modules/.bin/
/build/node_modules/jasmine-node/node_modules/.bin/
*.tmproj
/.build/
/local.properties
/extjs/local.properties
/tools/build/sencha-ant/nbproject/private
/tools/build/sencha-ant/build
/tools/build/sencha-ant/dist
atlassian-ide-plugin.xml
nbbuild.xml
/build/
/cache/
/.work/
chromedriver.log
/sencha-command-php/nbproject/private/
/sencha-command-php/build/
/sencha-command-php/dist/
/sencha-fashion/nbproject/private/
/sencha-fashion/build/
/sencha-fashion/dist/
/modules/

.idea/workspace.xml
.idea/tasks.xml
.idea/dictionaries

node_modules
!sencha-command/files/js/node_modules
work/build

sencha-npm/dist
#this file is autogenerated
sencha-npm/README.md
\.idea/codeStyles/codeStyleConfig\.xml

\.idea/codeStyles/Project\.xml
