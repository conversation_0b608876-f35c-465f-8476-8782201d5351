<ivysettings>
    <properties file="local.properties"/>
    <property name="sencha.maven.repo.url" value="https://sencha.myget.org/F/maven/auth/11954ec6-304d-4224-b815-5d6a44a0b01b/maven/" override="false"/>
    <settings defaultResolver="local-chain"/>

    <credentials host="maven.sencha.com" realm="Artifactory Realm" username="<EMAIL>" passwd="" />

    <resolvers>
        <ibiblio name="ibiblio-maven2" m2compatible="true"/>
        <ibiblio name="java-net-maven2" root="https://download.java.net/maven/2/" m2compatible="true" />
        <ibiblio name="maven-central" root="https://repo1.maven.org/maven2" m2compatible="true"/>
        <ibiblio name="maven" root="https://mvnrepository.com/artifact/" m2compatible="true" />
        <ibiblio name="sencha" m2compatible="true" root="${sencha.maven.repo.url}"/>
        <ibiblio name="artifactory" m2compatible="true" root="https://sencha.myget.org/F/internal/auth/db7f5253-5070-42ee-9e67-4912c00b7eca/maven/" />
        <ibiblio name="sencha-legacy" m2compatible="true" root="http://eye.sencha.com/artifactory/libs-release-local"/>
        <ibiblio name="swt-repo" m2compatible="true" root="https://swt-repo.googlecode.com/svn/repo/"/>
        <chain name="local-chain">
            <resolver ref="ibiblio-maven2"/>
            <resolver ref="maven-central"/>
            <resolver ref="sencha"/>
            <resolver ref="sencha-legacy"/>
            <resolver ref="artifactory"/>
            <resolver ref="swt-repo"/>
            <resolver ref="java-net-maven2"/>
            <resolver ref="maven"/>
        </chain>
    </resolvers>
</ivysettings>
