/*
 * Copyright (c) 2012-2017. Sencha Inc.
 */

package com.sencha.tools.compressors;

import com.google.javascript.jscomp.CompilerOptions;
import com.google.javascript.jscomp.parsing.parser.Parser;
import com.sencha.cli.annotations.Doc;
import com.sencha.core.cli.annotations.Private;
import com.sencha.logging.SenchaLogManager;
import org.slf4j.Logger;

public enum JsLanguageLevel {
    @Doc("ECMAScript 3 language level")
    ES3,

    @Doc("ECMAScript 5 language level")
    ES5,

    @Private
    ES5_STRICT,

    @Doc("ECMAScript 6 language level (2015)")
    ES6,

    @Private
    ES6_STRICT,

    @Doc("ECMAScript 7 language level (2016)")
    ES7,

    @Doc("ECMAScript 8 language level (2017)")
    ES8,

    @Doc("ECMAScript 9 language level (2018)")
    ES9,

    @Doc("ECMAScript 10 language level (2019)")
    ES10,

    @Doc("ECMAScript 11 language level (2020)")
    ES11,

    @Doc("ECMAScript 12 language level (2021)")
    ES12,

    @Doc("ECMAScript 13 language level (2022)")
    ES13,

    @Doc("ECMAScript 2015 language level (ES6)")
    ES2015,

    @Doc("ECMAScript 2016 language level (ES7)")
    ES2016,

    @Doc("ECMAScript 2017 language level (ES8)")
    ES2017,

    @Doc("ECMAScript 2018 language level (ES9)")
    ES2018,

    @Doc("ECMAScript 2019 language level (ES10)")
    ES2019,

    @Doc("ECMAScript 2020 language level (ES11)")
    ES2020,

    @Doc("ECMAScript 2021 language level (ES12)")
    ES2021,

    @Doc("ECMAScript 2022 language level (ES13)")
    ES2022,

    @Doc("ECMAScript Next (or ES.Next) language level")
    NEXT,

    @Doc("An output language level that matches the input level (disable transpiler)")
    ANY;

    private static final Logger _logger = SenchaLogManager.getLogger();

    public CompilerOptions.LanguageMode getClosureLanguageMode() {
        switch (this) {
            case ES3:
                return CompilerOptions.LanguageMode.ECMASCRIPT3;
            case ES5:
            case ES5_STRICT:
                return CompilerOptions.LanguageMode.ECMASCRIPT5;
            case ES6:
            case ES6_STRICT:
            case ES2015:
                return CompilerOptions.LanguageMode.ECMASCRIPT_2015;
            case ES7:
            case ES2016:
                return CompilerOptions.LanguageMode.ECMASCRIPT_2016;
            case ES8:
            case ES2017:
                return CompilerOptions.LanguageMode.ECMASCRIPT_2017;
            case ES9:
            case ES2018:
                return CompilerOptions.LanguageMode.ECMASCRIPT_2018;
            case ES10:
            case ES2019:
                return CompilerOptions.LanguageMode.ECMASCRIPT_2019;
            case ES11:
            case ES2020:
                return CompilerOptions.LanguageMode.ECMASCRIPT_2020;
            case ES12:
            case ES2021:
                return CompilerOptions.LanguageMode.ECMASCRIPT_2021;
            case ES13:
            // case ES2022:
            //     return CompilerOptions.LanguageMode.ECMASCRIPT_2022;
            case NEXT:
                return CompilerOptions.LanguageMode.ECMASCRIPT_NEXT;
            default:
                return CompilerOptions.LanguageMode.NO_TRANSPILE;
        }
    }

    public boolean needsTranspile() {
        return this == ES3 || this == ES5;
    }

    public boolean shouldTranspile(JsLanguageLevel output) {
        output = output.reduce();
        JsLanguageLevel reduced = reduce();
        switch(reduced) {
            case NEXT:
                return true;
            default:
                return reduced.ordinal() > output.ordinal();
        }
    }


    public Parser.Config.Mode getClosureParserConfigMode() {
        switch (this) {
            case ES3:
                return Parser.Config.Mode.ES3;
            case ES5:
            case ES5_STRICT:
                return Parser.Config.Mode.ES5;
            case ES6:
            case ES6_STRICT:
            case ES2015:
                return Parser.Config.Mode.ES6_OR_ES7;
            case ES7:
            case ES2016:
                return Parser.Config.Mode.ES6_OR_ES7;
            default:
                // ES8+ including ES9, ES10, ES11, ES2017, ES2018, ES2019, ES2020, ES2021, ES2022, NEXT
                return Parser.Config.Mode.ES8_OR_GREATER;
        }
    }

    public boolean isStrict() {
        return false;
    }

    private Parser.Config _closureParserConfig;

    public synchronized Parser.Config getClosureParserConfig() {
        if (_closureParserConfig == null) {
            _closureParserConfig = new Parser.Config(getClosureParserConfigMode(), isStrict());
        }
        return _closureParserConfig;
    }

    public boolean isES6orGreater() {
        switch(this) {
            case ES3:
            case ES5:
            case ES5_STRICT:
                return false;
            default:
                return true;
        }
    }

    public JsLanguageLevel reduce() {
        if (this == ES5_STRICT) {
            return ES5;
        }
        if (this == ES6_STRICT) {
            return ES6;
        }
        // Reduce year-based aliases to their ES# equivalents for consistency
        if (this == ES2015) {
            return ES6;
        }
        if (this == ES2016) {
            return ES7;
        }
        if (this == ES2017) {
            return ES8;
        }
        if (this == ES2018) {
            return ES9;
        }
        if (this == ES2019) {
            return ES10;
        }
        if (this == ES2020) {
            return ES11;
        }
        return this;
    }
}
