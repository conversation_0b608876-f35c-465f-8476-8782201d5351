/*
 * Copyright (c) 2012. Sencha Inc.
 */
package com.sencha.tools.compiler.sources;

import com.sencha.command.compile.SourceFileList;
import com.sencha.exceptions.ExBuild;
import com.sencha.exceptions.ExNotFound;
import com.sencha.exceptions.ExParse;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerMessage;
import com.sencha.tools.compiler.ast.js.BaseNode;
import com.sencha.tools.compiler.ast.js.RootNode;
import com.sencha.tools.compiler.sources.ClassPathScope.ExternalReferenceSpec;
import com.sencha.tools.compiler.sources.types.AutoDependency;
import com.sencha.tools.compiler.sources.types.ClassDefinition;
import com.sencha.tools.compiler.sources.types.ClassMember;
import com.sencha.util.PathUtil;
import com.sencha.util.StringUtil;
import org.slf4j.Logger;

import java.io.File;
import java.util.*;

public class SymbolCache implements ClassPathScopeSymbols {

    private static final Logger _logger = SenchaLogManager.getLogger();
    
    private List<SymbolCache> _parentCaches = new ArrayList<SymbolCache>();
    
    private SourceFileList _sourceFiles;
    
    private ClassPathScope _scope;
    
    private Map<SourceFile, List<Reference>> _references = 
        new LinkedHashMap<SourceFile, List<Reference>>();
    
    private Map<SourceFile, List<Dependency>> _dependencies = 
        new LinkedHashMap<SourceFile, List<Dependency>>();
    
    private Map<SourceFile, Set<SourceFile>> _overrides = 
        new LinkedHashMap<SourceFile, Set<SourceFile>>();
    
    private Map<ClassDefinition, Set<ClassDefinition>> _classOverrides = 
        new LinkedHashMap<ClassDefinition, Set<ClassDefinition>>();

    private DependencyBuffer _depBuffer = new DependencyBuffer();

    private Map<ClassDefinition, Map<String, AutoDependency>> _autoDepMap =
        new HashMap<ClassDefinition, Map<String, AutoDependency>>();

    private DefinedNameMap _globalDefines = new DefinedNameMap();
    private DefinedNameMap _globalTags = new DefinedNameMap();
    private DefinedNameMap _globalAliases = new DefinedNameMap();


    private DefinedNameMap _allGlobalDefines;
    private DefinedNameMap _allGlobalAliases;
    private DefinedNameMap _allGlobalTags;

    public SymbolCache(SourceFileList files, ClassPathScope scope, List<SymbolCache> parents) {
        _scope = scope;
        _sourceFiles = files;
        _parentCaches = parents;
    }

    @Override
    public ClassPathScope getScope() {
        return _scope;
    }
    
    public List<SymbolCache> getParentSymbolCaches() {
        return _parentCaches;
    }
    
    @Override
    public Collection<SourceFile> getSourceFiles() {
        return _sourceFiles;
    }
    
    @Override
    public Preprocessor getPreprocessor() {
        return getScope().getPreprocessor();
    }

    protected void addDefine(String className, SourceFile sf) {
        addDefine(className, sf, null);
    }

    protected void addDefine(String className, SourceFile currFile, ClassDefinition def) {
        if(_logger.isTraceEnabled()) {
            _logger.trace("adding defined name => {}", className);
        }
        _globalDefines.addDefinedName(className, currFile, def);
        if(_allGlobalDefines != null) {
            _allGlobalDefines.addDefinedName(className, currFile, def);
        }
    }

    protected void addTagFile(String tag, SourceFile file) {
        if(_logger.isTraceEnabled()) {
            _logger.trace("adding tag => {} : {}", tag, file.getCanonicalPath());
        }
        _globalTags.addDefinedName(tag, file);
    }

    protected void addAlias(String alias, ClassDefinition def) {
        if(_logger.isTraceEnabled()) {
            _logger.trace("adding alias => {} : {}", alias, def.getClassName());
        }
        _globalAliases.addDefinedName(alias, def);
    }

    private void checkForReplacements(String name, ClassDefinition def) {
        for (SymbolCache scope: getParentSymbolCaches()) {
            ClassDefinition current = scope.getClassForName(name);
            if (current != null) {
                current.replaceUsages(name, def);
            }
        }
    }

    public void accumulateSymbols() {
        _logger.debug("Caching symbol declarations");
        for (SourceFile sf : getSourceFiles()) {
            if(_logger.isDebugEnabled()) {
                _logger.info("accumulating symbols for file {}", sf.getCanonicalPath());
            }

            SourceFileSymbols symbols = sf.getSymbols();

            for (ClassDefinition def : symbols.getClasses()) {
                String className = def.getClassName();

                addDefine(className, sf, def);

                for (String alt : def.getAlternateClassNames()) {
                    addDefine(alt, sf, def);
                    checkForReplacements(alt, def);
                }

                for (String als : def.getAliases()) {
                    addAlias(als, def);
                    checkForReplacements(als, def);
                }
            }

            for (String define : symbols.getExplicitDefines()) {
                addDefine(define, sf);
            }

            for(String tag : symbols.getTags().keySet()) {
                addTagFile(tag, sf);
            }
        }
    }

    private void setOverrideTargetFile(SourceFile sf) {
        SourceFile file = null;
        String override = sf.getSymbols().getOverrideTarget();
        if(!StringUtil.isNullOrEmpty(override)) {
            ClassDefinition cls = getClassForName(override);
            if(cls != null) {
                file = cls.getSourceFile();
            } else {
                List<SourceFile> targets = getFilesForDefinedName(override);
                if(targets != null && targets.size() > 0) {
                    file = targets.get(0);
                } else {
                    String parent = sf.getDirectory();
                    String path = PathUtil.join(parent, override);
                    path = PathUtil.getCanonicalPath(path);
                    file = getScope().lookupSourceFile(new File(path));
                }
            }
        }
        if(file != null) {
            sf.getSymbols().setOverrideTargetFile(file);
        }
    }

    public void linkClassGraph() {
        _logger.debug("Linking class inheritance graph");

        for(SourceFile sf : getSourceFiles()) {
            String targetName = sf.getSymbols().getOverrideTarget();
            if(!StringUtil.isNullOrEmpty(targetName)) {
                setOverrideTargetFile(sf);
                for(SourceFile target : getFilesForDefinedName(targetName)) {
                    addOverride(target, sf);
                }
            }

            for(ClassDefinition cls : sf.getSymbols().getClasses()) {
                String className = cls.getClassName();

                if(_logger.isTraceEnabled()) {
                    _logger.trace("processing graph for class : {}", className);
                }

                if (cls.getExtendName() != null) {
                    String baseName = resolveClassName(cls.getExtendName());
                    ClassDefinition base =
                        getClassForName(baseName);

                    if (base == null) {
                        if (!getDefinitionMap()
                            .containsKey(resolveClassName(baseName))) {
                            _logger.error(
                                "Failed to resolve dependency {} for file {}",
                                baseName,
                                className);
                            throw new ExNotFound("Unknown definition for dependency : {0}",
                                baseName).raise();
                        }

                    } else {
                        cls.setBaseClass(base);
                    }
                } else {
                    SourceFile sourceFile = cls.getSourceFile();
                    SourceFileSymbols symbols = sourceFile.getSymbols();

                    // if not a core file, we need a requirement on core
                    Map<String, Boolean> tags = symbols.getTags();

                    String[] tagsForClasses = StringUtil.split(_scope.getTagsForClassDefs());

                    for(String tag : tagsForClasses) {
                        if(!(tags == null || tags.containsKey(tag) || sourceFile.isCoreFile())) {
                            addReference(sf, new Reference(
                                "@" + tag,
                                ReferenceType.ClassSystem,
                                sourceFile,
                                cls.getNode()));
                        }
                    }
                }

                cls.getMixedInClasses().clear();

                for (String mixin : cls.getMixins()) {
                    ClassDefinition mix = getClassForName(mixin);
                    if (mix != null) {
                        cls.addMixin(mix);
                    }
                }

                if(cls.getOverrideName() != null) {
                    ClassDefinition def = getClassForName(cls.getOverrideName());
                    if(def != null) {
                        cls.setOverrideTarget(def);
                        addOverride(def, cls);
                    }
                }
            }
        }
    }

    public void addOverride(ClassDefinition target, ClassDefinition override) {
        Set<ClassDefinition> overrides = _classOverrides.get(target);
        if(overrides == null) {
            overrides = new LinkedHashSet<ClassDefinition>();
            _classOverrides.put(target, overrides);
        }
        overrides.add(override);
        addOverride(target.getSourceFile(), override.getSourceFile());
    }

    public void addOverride(SourceFile target, SourceFile override) {
        Set<SourceFile> overrides = _overrides.get(target);
        if(overrides == null) {
            overrides = new LinkedHashSet<SourceFile>();
            _overrides.put(target, overrides);
        }
        overrides.add(override);
    }

    private Map<SourceFile, Set<SourceFile>> _allFileOverrides;
    private Map<ClassDefinition, Set<ClassDefinition>> _allClassOverrides;

    @Override
    public synchronized Set<ClassDefinition> getOverrides(ClassDefinition target) {
        if(_allClassOverrides == null) {
            _allClassOverrides = new HashMap<ClassDefinition, Set<ClassDefinition>>();
        }
        Set<ClassDefinition> overrides = _allClassOverrides.get(target);
        if(overrides == null) {
            overrides = new LinkedHashSet<ClassDefinition>();
            for(SymbolCache parent : getParentSymbolCaches()) {
                overrides.addAll(parent.getOverrides(target));
            }
            if(_classOverrides.containsKey(target)){
                overrides.addAll(_classOverrides.get(target));
            }
            _allClassOverrides.put(target, overrides);
        }
        return overrides;
    }

    @Override
    public synchronized Set<SourceFile> getOverrides(SourceFile target) {
        if(_allFileOverrides == null) {
            _allFileOverrides = new HashMap<SourceFile, Set<SourceFile>>();
        }
        Set<SourceFile> overrides = _allFileOverrides.get(target);
        if(overrides == null) {
            overrides = new LinkedHashSet<SourceFile>();
            for(SymbolCache parent : getParentSymbolCaches()) {
                overrides.addAll(parent.getOverrides(target));
            }
            if(_overrides.containsKey(target)) {
                overrides.addAll(_overrides.get(target));
            }
            _allFileOverrides.put(target, overrides);
        }
        return overrides;
    }

    public void accumulateReferences() {
        for(SourceFile sf : getSourceFiles()) {
            List<Reference> refs = _references.get(sf);
            if(refs == null) {
                refs = new ArrayList<Reference>();
                _references.put(sf, refs);
            }
            refs.addAll(sf.getSymbols().getReferences());
        }
    }

    private void setScopeAutoDependencies(SourceFile sf) {
        for(ClassDefinition def : sf.getSymbols().getClasses()) {
            def.setScopeAutoDependencies(getAutoDependencies(def));
        }
    }

    private void loadScopeAutodependencies() {
        for(SourceFile sf : getSourceFiles()) {
            setScopeAutoDependencies(sf);
        }
    }

    public void detectAssignments() {
        _logger.debug("Collecting assignments");
        AssignmentVisitor assignments = new AssignmentVisitor();
        assignments.setContext(this);
        for(SourceFile sf : getSourceFiles()) {
            try {
                assignments.setCurrentFile(sf);
                RootNode root = sf.getAstRoot();
                assignments.visit(root);
            } catch (Exception e) {
                throw new ExParse(e, "Failed processing assignments for {0}", sf.getCanonicalPath()).raise();
            }
        }
    }

    public void detectReferences() {
        _logger.debug("Collecting references");
        ReferenceVisitor references = new ReferenceVisitor();
        references.setContext(this);
        for(SourceFile sf : getSourceFiles()) {
            try {
                references.setCurrentFile(sf);
                RootNode root = sf.getAstRoot();
                references.visit(root);
            } catch (Exception e) {
                throw new ExParse(e, "Failed processing references for {0}", sf.getCanonicalPath()).raise();
            }
        }
    }

    public void processSymbols() {
        _logger.debug("Analyzing all symbol declarations");
        accumulateReferences();
        accumulateSymbols();
//        detectAssignments();
        processExternalReferences();
        linkClassGraph();
        loadScopeAutodependencies();
        detectReferences();
        linkAutoOverrides();
        resolveDependencies();
    }
    
    public final void processExternalReferences() {
        ClassPathScope scope = getScope();
        Collection<ExternalReferenceSpec> refs = scope.getExternalReferences();

        if(_logger.isDebugEnabled()) {
            _logger.debug("adding external references for scope {}", scope.getIdentifier());
        }
        
        for(ExternalReferenceSpec ref : refs) {
            List<SourceFile> files = new ArrayList<SourceFile>();
            
            if(ref.isFile()) {
                SourceFile sf = scope.lookupSourceFile(ref.getFile());
                if(sf != null) {
                    files.add(sf);
                }
            } else {
                files.addAll(getFilesForDefinedName(ref.getSrcName()));
            }

            if(files.isEmpty()) {
                _logger.warn("unable to locate files for external reference : {}", ref.getSrcName());
                _logger.warn("scope is {}", getScope().getIdentifier());
            } else {
                for(SourceFile sf : files) {
                    if(_logger.isDebugEnabled()) {
                        _logger.debug("adding reference : {} => {}", sf.getCanonicalPath(), ref.getRefName());
                    }
                    ReferenceType type = ref.isUses()
                        ? ReferenceType.ExternalUses
                        : ReferenceType.ExternalRequirement;
                    ref.setRefName(ref.getRefName().trim());
                    Reference r = new Reference(ref.getRefName(), type, sf);
                    if(ref.getRefName().endsWith(".js")) {
                        r.setIsFileReference(true);
                    }
                    r.setAllowNoMatchingFiles(ref.isAllowNoMatchingFiles());
                    r.setExternal(true);
                    addReference(sf, r);
                }
            }
        }
    }

    public void linkAutoOverrides() {
        for(SourceFile sf : getSourceFiles()) {
            SourceFileSymbols symbols = sf.getSymbols();
            if(symbols.isAutoOverride()) {
                SourceFile target = symbols.getOverrideTargetFile();
                if(target != null) {
                    _logger.debug("Detected auto override of {} from {}", target.getCanonicalPath(), sf.getCanonicalPath());
                    Reference ref = new Reference(sf.getCanonicalPath(), ReferenceType.AutoOverrideRequirement, target);
                    ref.setIsFileReference(true);
                    addReference(target, ref);
                } else if(_logger.isDebugEnabled()) {
                    _logger.warn("Detected auto override {}, but no target {} not found", sf.getCanonicalPath(), symbols.getOverrideTarget());
                }
            }
        }
    }

    @Override
    public DefinedNameMap getAllGlobalDefines() {
        if(_allGlobalDefines == null) {
            _allGlobalDefines = new DefinedNameMap();
            for(SymbolCache parent : getParentSymbolCaches()) {
                _allGlobalDefines.merge(parent.getAllGlobalDefines());
            }
            _allGlobalDefines.merge(_globalDefines);
        }
        return _allGlobalDefines;
    }

    @Override
    public DefinedNameMap getAllGlobalAliases() {
        if(_allGlobalAliases == null) {
            _allGlobalAliases = new DefinedNameMap();
            for(SymbolCache parent : getParentSymbolCaches()) {
                _allGlobalAliases.merge(parent.getAllGlobalAliases());
            }
            _allGlobalAliases.merge(_globalAliases);
        }
        return _allGlobalAliases;
    }

    @Override
    public DefinedNameMap getAllGlobalTags() {
        if(_allGlobalTags == null) {
            _allGlobalTags = new DefinedNameMap();
            for(SymbolCache parent : getParentSymbolCaches()) {
                _allGlobalTags.merge(parent.getAllGlobalTags());
            }
            _allGlobalTags.merge(_globalTags);
        }
        return _allGlobalTags;
    }

    @Override
    public Map<String, ClassDefinition> getAllClasses() {
        return getAllGlobalDefines().getClassMap();
    }

    @Override
    public Map<String, String> getAlternateClassNames() {
        Map<String, String> altMap = new HashMap<String, String>();
        for(SymbolCache parent : getParentSymbolCaches()) {
            altMap.putAll(parent.getAlternateClassNames());
        }
        for(ClassDefinition def : getAllClasses().values()) {
            for(String altName : def.getAlternateClassNames()) {
                altMap.put(altName, def.getClassName());
            }
        }
        return altMap;
    }

    @Override
    public Map<String, String> getAliases() {
        Map<String, String> alsMap = new HashMap<String, String>();
        for(SymbolCache parent : getParentSymbolCaches()) {
            alsMap.putAll(parent.getAliases());
        }
        for(ClassDefinition def : getAllGlobalAliases().getClassMap().values()) {
            for(String alias : def.getAliases()) {
                alsMap.put(alias, def.getClassName());
            }
        }
        return alsMap;
    }

    private Map<String, String> _nameResolveMap;
    
    private Map<String, String> getResolveMap() {
        if(_nameResolveMap == null) {
            _nameResolveMap = new HashMap<String, String>();
            for(SymbolCache parent : getParentSymbolCaches()) {
                _nameResolveMap.putAll(parent.getResolveMap());
            }
            _nameResolveMap.putAll(getAliases());
            _nameResolveMap.putAll(getAlternateClassNames());
        }
        return _nameResolveMap;
    }
    
    @Override
    public String resolveClassName(String name) {
        Map<String, String> map = getResolveMap();
        if(map.containsKey(name)) {
            return map.get(name);
        }
        return name;
    }
    
    @Override
    public ClassDefinition getClassForName(String clsName) {
        String cName = resolveClassName(clsName);
        if(!StringUtil.isNullOrEmpty(cName)) {
            return getAllClasses().get(cName);
        }
        if(_logger.isDebugEnabled()) {
            _logger.debug("Failed to lookup class for name {}", clsName);
        }
        return null;
    }

    @Override
    public List<ClassDefinition> getOverridesForMixin(String mixin) {
        List<ClassDefinition> overrides = new ArrayList<ClassDefinition>();
        String resolvedMixinName = resolveClassName(mixin);
        for(ClassDefinition def : getAllClasses().values()){
            if(!StringUtil.isNullOrEmpty(def.getOverrideName())) {
                String overrideName = resolveClassName(def.getOverrideName());
                if(resolvedMixinName.equals(overrideName)) {
                    overrides.add(def);
                }
            }
        }
        return overrides;
    }
    
    @Override
    public List<SourceFile> getFilesForDefinedName(String name) {
        boolean fullGlob = false;

        if("*".equals(name)) {
            fullGlob = true;
            name = "Ext.*";
        }

        List<SourceFile> files = new ArrayList<SourceFile>();
        
        name = resolveClassName(name);
        ClassDefinition cls = getClassForName(name);

        if(cls != null) {
            files.add(cls.getSourceFile());
        } else if(name.startsWith("@")) {
            name = name.substring(1);
            if(getTagMap().containsKey(name)) {
                files.addAll(getTagMap().get(name));
            }
        } else {
            if(name.endsWith("*")) {
                name = name.substring(0, name.length() - 1);
                for(Map.Entry<String, DefinedNameMap> entry : getAllGlobalDefines().flatten().entrySet()) {
                    String key = entry.getKey();
                    if(key.startsWith(name)) {
                        DefinedNameMap names = entry.getValue();
                        ClassDefinition def = names.getClassDefinition();
                        if(def != null && def.isVirtual()) {
                            continue;
                        }
                        files.addAll(names.getSourceFiles());
                    }
                }
            } else {
                if(getDefinitionMap().containsKey(name)) {
                    files.addAll(getDefinitionMap().get(name));
                }
            }
        }
        return files;
    }

    @Override
    public Set<String> getDefinedNames() {
        return getDefinitionMap().keySet();
    }

    public synchronized Map<String, List<SourceFile>> getDefinitionMap() {
        return getAllGlobalDefines().getSourceFileMap();
    }

    public synchronized Map<String, List<SourceFile>> getTagMap() {
        return getAllGlobalTags().getSourceFileMap();
    }


    @Override
    public List<String> getShallowXTypesForClass (String classname) {
        classname = resolveClassName(classname);
        ClassDefinition def = getClassForName(classname);
        List<String> xtypes = new ArrayList<String>();
        if (def != null) {
            xtypes.addAll(def.getShallowXTypes());
        }
        return xtypes;
    }
    
    @Override
    public List<String> getXTypesForClass(String classname) {
        classname = resolveClassName(classname);
        ClassDefinition def = getClassForName(classname);
        List<String> xtypes = new ArrayList<String>();
        if (def != null) {
            xtypes.addAll(def.getAllXTypes());
        }
        return xtypes;
    }

    @Override
    public void resolveDependencies() {
        resolveDependencies(false);
    }
    
    @Override
    public void resolveDependencies(boolean clearDeps) {
        resolveDependencies(clearDeps, getScope());
    }

    @Override
    public void resolveDependencies(boolean clearDeps, ClassPathScope scope) {
        if(clearDeps) {
            _dependencies.clear();
            _depBuffer = new DependencyBuffer();
        }
        String dependencyMode = getScope().getDependenciesSetting(); // or 'warn' or 'none'
        for(Map.Entry<SourceFile, List<Reference>> entry : _references.entrySet()) {
            SourceFile sourceFile = entry.getKey();
            List<Reference> references = entry.getValue();
            boolean isOptimizerFile = false;

            if(sourceFile.getSymbols().isOptimizerFile()) {
                isOptimizerFile = true;
            }

            Map<SourceFile, List<Reference>> refsByTarget =
                new LinkedHashMap<SourceFile, List<Reference>>();
            
            for(Reference ref : references) {

                if(StringUtil.isNullOrEmpty(ref.getReferenceName())) {
                    CompilerMessage.UnknownNameLookup.log(ref.getNode());
                    continue;
                }

                List<SourceFile> files = new ArrayList<SourceFile>();

                if (ref.isFileReference()) {
                    String filename = ref.getReferenceName();

                    if (!PathUtil.isAbsolute(filename)) {
                        filename = PathUtil.getAbsolutePath(new File(
                            ref.getSourceFile().getDirectory(),
                            filename));
                    }

                    SourceFile targetSourceFile =
                        scope.getSourceFile(filename);

                    if (targetSourceFile != null) {
                        files.add(targetSourceFile);
                    }
                } else {
                    String refName = resolveClassName(ref.getReferenceName());
                    files.addAll(getFilesForDefinedName(refName));
                }

                if (files.isEmpty()) {
                    if (!ref.allowNoMatchingFiles()) {
                        Boolean throwException = true;
                        if (ref.isUses()) {
                            CompilerMessage.UnmetUsesError.log(ref.getNode(), ref.getReferenceName());
                        } else {
                            if (ignoreLocalePackageOverride(sourceFile, ref))
                            {
                                throwException = false;
                            } else {
                                CompilerMessage.UnmetRequirementError.log(ref.getNode(), ref.getReferenceName());
                            }
                        }
                        if (throwException) {
                            throw new ExBuild("Failed to find any files for " + ref).raise();
                        }
                    } else {
                        if (ref.isUses()) {
                            if (ref.isExternal()) {
                                CompilerMessage.UnmetExternalUsesWarn.log(ref.getNode(), ref.getReferenceName());
                            } else {
                                CompilerMessage.UnmetUsesWarn.log(ref.getNode(), ref.getReferenceName());
                            }
                        } else {
                            if (ref.isExternal()) {
                                CompilerMessage.UnmetExternalRequirementWarn.log(ref.getNode(), ref.getReferenceName());
                            } else {
                                CompilerMessage.UnmetRequirementWarn.log(ref.getNode(), ref.getReferenceName());
                            }
                        }
                    }
                }

                for (SourceFile sf : files) {
                    List<Reference> refs = refsByTarget.get(sf);
                    if(refs == null) {
                        refs = new ArrayList<Reference>();
                        refsByTarget.put(sf, refs);
                    }
                    refs.add(ref);
                }
            }

            ClassPathScope sfScope = sourceFile.getScope();
            Set<BaseNode> logged = new HashSet<BaseNode>();
            
            for(Map.Entry<SourceFile, List<Reference>> refEntry: refsByTarget.entrySet()) {
                SourceFile sf = refEntry.getKey();
                if(isOptimizerFile) {
                    if(!sf.getSymbols().isOptimizerFile()) {
                        _logger.debug("Skipping ref to file {} for file {}", sf, sourceFile);
                        continue;
                    } 
                    else {
                        _logger.debug("Optimizer file {} had reference to file {}", sourceFile, sf);
                    }
                }
                ClassPathScope s = sf.getScope();
                if (!s.isDirectlyRequired() && (s != sfScope)) {
                    _logger.debug("Skipping ref to file {} for file {}", sf, sourceFile);
                    continue;
                }
                if (!sourceFile.getCanonicalPath().equals(sf.getCanonicalPath())) {
                    Dependency d = new Dependency(sourceFile, sf);
                    List<Reference> refs = refEntry.getValue();
                    for(Reference ref : refs) {
                        BaseNode node = ref.getNode();
                        boolean hasTag = node != null && 
                            ref.getNode().hasTag("cmd-ignore-reference"); 
                        if (!hasTag) {
                            d.addReference(ref);
                        }
                    }

                    if (d.getReferences().size() > 0) {
                        addDependency(sourceFile, d);

                        if ("usages".equals(dependencyMode)) {
                            if (!d.hasUsage()) {
                                d.setTransitive(false);
                                logIf(CompilerMessage.RequiredButNotUsed, d, logged);
                            }
                        }
                        else if (d.isExplicit()) {
                            if (!d.hasUsage() && !d.hasGlobReference()) {
                                logIf(CompilerMessage.RequiredButNotUsed, d, logged);
                            }
                        }
                        else if (!"auto".equals(dependencyMode)) {
                            d.setTransitive(false);
                            if ("warn".equals(dependencyMode)) {
                                logIf(CompilerMessage.ReferencedButNotRequired, d, logged);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     *
     * If the sourcefile is an override, such as in the locale package,
     * AND if the reference type is a require
     * AND if the reference isn't a defined class
     * THEN we should not throw an exception
     *
     * Overrides should never require classes.
     *
     * J.N.
     */
    private static final String PATH_SEGMENT_OVERRIDES = "overrides";
    private static final String PATH_SEGMENT_LOCALE = "locale";
    private boolean ignoreLocalePackageOverride(SourceFile sourceFile, Reference ref) {
        return sourceFile.getCanonicalPath().contains(PATH_SEGMENT_OVERRIDES)
                && getScope().getBasePath().contains(PATH_SEGMENT_LOCALE)
                && !isNameDefined(ref.getReferenceName());
    }

    private void logIf(CompilerMessage msg, Dependency d, Set<BaseNode> logged) {
        if (msg.isEnabled()) {
            Reference r = d.getReferences().get(0);
            BaseNode node = r.getNode();
            if (!logged.contains(node)) {
                msg.log(node, r.getReferenceName());
                logged.add(node);
            }
        }
    }
    
    @Override
    public void addReference(SourceFile sourceFile, Reference reference) {
        List<Reference> refs = _references.get(sourceFile);
        if(refs == null) {
            refs = new ArrayList<Reference>();
            _references.put(sourceFile, refs);
        }
        refs.add(reference);
    }

    @Override
    public void addDependency(SourceFile sourceFile, Dependency dependency) {
        List<Dependency> deps = _dependencies.get(sourceFile);
        if(deps == null) {
            deps = new ArrayList<Dependency>();
            _dependencies.put(sourceFile, deps);
        }
        deps.add(dependency);
    }
    
    public List<Reference> getReferences(SourceFile sourceFile) {
        List<Reference> refs = new ArrayList<Reference>();
        for(SymbolCache symbols : getParentSymbolCaches()) {
            refs.addAll(symbols.getReferences(sourceFile));
        }
        if(_references.containsKey(sourceFile)) {
            refs.addAll(_references.get(sourceFile));
        }
        return refs;
    }
    
    public Collection<Dependency> getDependencies(SourceFile sourceFile) {
        List<Dependency> deps = new ArrayList<Dependency>();
        for(SymbolCache symbols : getParentSymbolCaches()) {
            deps.addAll(symbols.getDependencies(sourceFile));
        }
        if(_dependencies.containsKey(sourceFile)) {
            deps.addAll(_dependencies.get(sourceFile));
        }
        return deps;
    }
    
    @Override
    public Collection<Dependency> getDependencies(SourceFile sourceFile, Dependency.Type type) {
        List<Dependency> deps = new ArrayList<Dependency>();
        for(Dependency d : getDependencies(sourceFile)) {
            if(d.getType() == type) {
                deps.add(d);
            }
        }
        return deps;
    }

    @Override
    public Collection<Dependency> getRequirements(SourceFile sourceFile) {
        return getDependencies(sourceFile, Dependency.Type.Requirement);
    }

    @Override
    public Collection<Dependency> getUses(SourceFile sourceFile) {
        return getDependencies(sourceFile, Dependency.Type.Uses);
    }

    @Override
    public boolean isNameDefined(String name) {
        return isNameDefined(name, true);
    }

    @Override
    public boolean isNameDefined(String name, boolean includeAliases) {
        if(!StringUtil.isNullOrEmpty(name)) {
            name = name.trim();
            if (includeAliases) {
                name = resolveClassName(name);
            }
            return getDefinitionMap().containsKey(name);
        }
        return false;
    }
    
    @Override
    public Set<ClassMember> getDefinedMembers(ClassDefinition def, String name) {
        Set<ClassMember> members = new HashSet<ClassMember>();
        for(ClassDefinition base : def.getAllBaseClasses()) {
            members.addAll(getDefinedMembers(base, name));
        }
        Set<ClassDefinition> overrides = getOverrides(def);
        for(ClassDefinition override : overrides) {
            members.addAll(getDefinedMembers(override, name));
        }
        ClassMember member = def.getMembers().get(name);
        if(member != null) {
            members.add(member);
        }
        return members;
    }

    @Override
    public DependencyBuffer getDependencyBuffer() {
        return _depBuffer;
    }


    @Override
    public Map<String, AutoDependency> getAutoDependencies(ClassDefinition def) {
        Map<String, AutoDependency> map = _autoDepMap.get(def);
        if(map == null) {
            map = new LinkedHashMap<String, AutoDependency>();
            map.putAll(def.getFrameworkAutoDependencies());
            map.putAll(def.getDetectedAutoDependencies());
            for(ClassDefinition override :  getOverrides(def)) {
                map.putAll(getAutoDependencies(override));
            }
            _autoDepMap.put(def, map);
        }
        return map;
    }
}
