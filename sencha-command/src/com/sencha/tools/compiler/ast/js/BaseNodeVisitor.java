/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.ast.js;

import com.sencha.tools.compiler.sources.SourceFile;

public class BaseNodeVisitor<T> implements NodeVisitor<T> {
    private T _context;
    private SourceFile _currentFile;

    @Override
    public void setContext(T context) {
        _context = context;
    }

    @Override
    public void visit(BaseNode node) {
        if(node != null) {
            node.doVisit(this);
        }
    }

    @Override
    public T getContext() {
        return _context;
    }

    public void walk(BaseNode node) {
        node.descend(this);
    }

    @Override
    public void onObjectLiteral(ObjectLiteral node) {
        node.descend(this);
    }

    @Override
    public void onObjectProperty(ObjectProperty node) {
        node.descend(this);
    }

    @Override
    public void onStringLiteral(StringLiteral node) {
    }

    @Override
    public void onBlock(Block node) {
        node.descend(this);
    }

    @Override
    public void onLineComment(LineComment node) {
    }

    @Override
    public void onBlockComment(BlockComment node) {
    }

    @Override
    public void onScope(Scope node) {
        node.descend(this);
    }

    @Override
    public void onArrayLiteral(ArrayLiteral node) {
        node.descend(this);
    }

    @Override
    public void onConditionalExpression(ConditionalExpression node) {
        node.descend(this);
    }

    @Override
    public void onAssignment(Assignment node) {
        node.descend(this);
    }

    @Override
    public void onInfix(Infix node) {
        node.descend(this);
    }

    @Override
    public void onElementGet(ElementGet node) {
        node.descend(this);
    }

    @Override
    public void onUnary(Unary node) {
        node.descend(this);
    }

    @Override
    public void onEmptyExpression(EmptyExpression node) {
    }

    @Override
    public void onError(ErrorNode node) {
    }

    @Override
    public void onExpressionStatement(ExpressionStatement node) {
        node.descend(this);
    }

    @Override
    public void onFunctionCall(FunctionCall node) {
        node.descend(this);
    }

    @Override
    public void onIfStatement(IfStatement node) {
        node.descend(this);
    }

    @Override
    public void onJumpNode(JumpNode node) {
        node.descend(this);
    }

    @Override
    public void onKeywordLiteral(KeywordLiteral node) {
        node.descend(this);
    }

    @Override
    public void onLabel(Label node) {
    }

    @Override
    public void onLabeledStatement(LabeledStatement node) {
        node.descend(this);
    }

    @Override
    public void onName(Name node) {
        node.descend(this);
    }

    @Override
    public void onNumberLiteral(NumberLiteral node) {
    }

    @Override
    public void onParenthesizedExpression(ParenthesizedExpression node) {
        node.descend(this);
    }

    @Override
    public void onRegExpLiteral(RegExpLiteral node) {
    }

    @Override
    public void onReturnStatement(ReturnStatement node) {
        node.descend(this);
    }

    @Override
    public void onSwitchCase(SwitchCase node) {
        node.descend(this);
    }

    @Override
    public void onThrowStatement(ThrowStatement node) {
        node.descend(this);
    }

    @Override
    public void onTryStatement(TryStatement node) {
        node.descend(this);
    }

    @Override
    public void onVariableDeclaration(VariableDeclaration node) {
        node.descend(this);
    }

    @Override
    public void onVariableInitializer(VariableInitializer node) {
        node.descend(this);
    }

    @Override
    public void onWithStatement(WithStatement node) {
        node.descend(this);
    }

    @Override
    public void onXmlFragment(XmlFragment node) {
        node.descend(this);
    }

    @Override
    public void onXmlLiteral(XmlLiteral node) {
        node.descend(this);
    }

    @Override
    public void onXmlRef(XmlRef node) {
        node.descend(this);
    }

    @Override
    public void onYieldStatement(YieldStatement node) {
        node.descend(this);
    }

    @Override
    public void onScriptNode(ScriptNode node) {
        node.descend(this);
    }

    @Override
    public void onFunctionNode(FunctionNode node) {
        node.descend(this);
    }

    @Override
    public void onBreakStatement(BreakStatement node) {
        node.descend(this);
    }

    @Override
    public void onContinueStatement(ContinueStatement node) {
        node.descend(this);
    }

    @Override
    public void onSwitchStatement(SwitchStatement node) {
        node.descend(this);
    }

    @Override
    public void onForInLoop(ForInLoop node) {
        node.descend(this);
    }

    @Override
    public void onArrayComprehensionLoop(ArrayComprehensionLoop node) {
        node.descend(this);
    }

    @Override
    public void onArrayComprehension(ArrayComprehension node) {
        node.descend(this);
    }

    @Override
    public void onLoop(Loop node) {
        node.descend(this);
    }

    @Override
    public void onLetNode(LetNode node) {
        node.descend(this);
    }

    @Override
    public void onDoLoop(DoLoop node) {
        node.descend(this);
    }

    @Override
    public void onForLoop(ForLoop node) {
        node.descend(this);
    }

    @Override
    public void onWhileLoop(WhileLoop node) {
        node.descend(this);
    }

    @Override
    public void onNewExpression(NewExpression node) {
        node.descend(this);
    }

    @Override
    public void onRootNode(RootNode node) {
        node.descend(this);
    }

    @Override
    public void onPropertyGet(PropertyGet node) {
        node.descend(this);
    }

    @Override
    public void onCatchClause(CatchClause node) {
        node.descend(this);
    }

    @Override
    public void onClassDeclaration(ClassDeclaration node) {
        node.descend(this);
    }

    @Override
    public void onGetAccessor(GetAccessor node) {
        node.descend(this);
    }

    @Override
    public void onSetAccessor(SetAccessor node) {
        node.descend(this);
    }

    @Override
    public void onDefaultParameter(DefaultParameter node) {
        node.descend(this);
    }

    @Override
    public void onSpreadExpression(SpreadExpression node) {
        node.descend(this);
    }

    @Override
    public void onObjectSpread(ObjectSpread node) {
        node.descend(this);
    }

    @Override
    public void onOptionalMemberExpression(OptionalMemberExpression node) {
        node.descend(this);
    }

    @Override
    public void onRestParameter(RestParameter node) {
        node.descend(this);
    }

    @Override
    public void onImportSpecifier(ImportSpecifier node) {
        node.descend(this);
    }

    @Override
    public void onImportDeclaration(ImportDeclaration node) {
        node.descend(this);
    }

    @Override
    public void onExportDeclaration(ExportDeclaration node) {
        node.descend(this);
    }

    @Override
    public void onExportSpecifier(ExportSpecifier node) {
        node.descend(this);
    }

    @Override
    public void onForOfLoop(ForOfLoop node) {
        node.descend(this);
    }

    @Override
    public void onAwaitExpression(AwaitExpression node) {
        node.descend(this);
    }

    @Override
    public void onTemplateLiteralExpression(TemplateLiteralExpression node) {
        node.descend(this);
    }

    @Override
    public void onTemplateLiteralPortion(TemplateLiteralPortion node) {
        node.descend(this);
    }

    @Override
    public void onTemplateSubstitution(TemplateSubstitution node) {
        node.descend(this);
    }

    @Override
    public void onComputedName(ComputedName node) {
        node.descend(this);
    }

    @Override
    public void onArrayPattern(ArrayPattern node) {
        node.descend(this);
    }

    @Override
    public void onObjectPattern(ObjectPattern node) {
        node.descend(this);
    }

    @Override
    public void onFormalParameterList(FormalParameterList node) {
        node.descend(this);
    }

    @Override
    public void onOptionalMemberLookUpExpression(OptionalMemberLookUpExpression node) {
        node.descend(this);
    }

    @Override
    public void onForAwaitOfStatement(ForAwaitOfStatement node) {
        node.descend(this);
    }

    @Override
    public void onPassthroughNode(PassthroughNode node) {
        // PassthroughNode has no child nodes to visit since it preserves
        // the original source text without AST transformation
    }

    public SourceFile getCurrentFile() {
        return _currentFile;
    }

    public void setCurrentFile(SourceFile currentFile) {
        _currentFile = currentFile;
    }

}
