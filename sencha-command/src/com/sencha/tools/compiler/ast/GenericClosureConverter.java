/*
 * Copyright (c) 2012-2024. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.parsing.parser.trees.*;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.ast.js.*;
import org.slf4j.Logger;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * GenericClosureConverter - A pattern-based AST converter that reduces maintenance overhead
 * by using generic expression patterns instead of individual converter methods for every
 * JavaScript syntax feature.
 * 
 * This converter:
 * - Uses ~10 pattern-based converters instead of 50+ individual methods
 * - Automatically handles new JavaScript syntax through reflection and patterns
 * - Creates GenericExpression nodes for unknown/modern syntax
 * - Maintains backward compatibility with existing NodeVisitor pattern
 * - Future-proofs the system against new JavaScript additions
 */
public class GenericClosureConverter extends ClosureConverter implements ClosureASTConverter {
    
    private static final Logger _logger = SenchaLogManager.getLogger();
    
    private String _sourceName;
    private String _originalSource;
    
    // Pattern handlers for different expression types
    private final Map<String, ExpressionPatternHandler> _patternHandlers;
    
    // Core syntax that needs specific handling (keep existing converters)
    private final Set<Class<?>> _coreConverters;
    
    public GenericClosureConverter() {
        super();
        _patternHandlers = initializePatternHandlers();
        _coreConverters = initializeCoreConverters();
    }
    
    public void setSourceName(String sourceName) {
        this._sourceName = sourceName;
    }
    
    public void setOriginalSource(String originalSource) {
        this._originalSource = originalSource;
    }
    
    /**
     * Initialize pattern handlers for different expression types
     */
    private Map<String, ExpressionPatternHandler> initializePatternHandlers() {
        Map<String, ExpressionPatternHandler> handlers = new HashMap<>();
        
        // Unary expression pattern (await, typeof, delete, etc.)
        handlers.put("UnaryExpression", new UnaryExpressionHandler());
        
        // Binary expression pattern (||, &&, ??, +, -, etc.)
        handlers.put("BinaryExpression", new BinaryExpressionHandler());
        
        // Call expression pattern (function calls, optional calls, etc.)
        handlers.put("CallExpression", new CallExpressionHandler());
        
        // Member expression pattern (property access, optional chaining, etc.)
        handlers.put("MemberExpression", new MemberExpressionHandler());
        
        // Literal expression pattern (strings, numbers, booleans, etc.)
        handlers.put("LiteralExpression", new LiteralExpressionHandler());
        
        // Assignment expression pattern (=, +=, -=, etc.)
        handlers.put("AssignmentExpression", new AssignmentExpressionHandler());
        
        // Update expression pattern (++, --)
        handlers.put("UpdateExpression", new UpdateExpressionHandler());
        
        // Conditional expression pattern (ternary operator)
        handlers.put("ConditionalExpression", new ConditionalExpressionHandler());
        
        return handlers;
    }
    
    /**
     * Initialize core converters that need specific handling
     */
    private Set<Class<?>> initializeCoreConverters() {
        Set<Class<?>> core = new HashSet<>();
        
        // Core syntax that should use existing specific converters
        core.add(ProgramTree.class);
        core.add(BlockTree.class);
        core.add(FunctionDeclarationTree.class);
        core.add(VariableDeclarationTree.class);
        core.add(IfStatementTree.class);
        core.add(ForStatementTree.class);
        core.add(WhileStatementTree.class);
        core.add(ReturnStatementTree.class);
        core.add(ExpressionStatementTree.class);
        core.add(ObjectLiteralExpressionTree.class);
        core.add(ArrayLiteralExpressionTree.class);
        
        return core;
    }
    
    /**
     * Universal convert method that uses pattern detection
     */
    @Override
    public BaseNode convert(ParseTree tree) {
        return doConvert(tree, null);
    }
    
    /**
     * Main conversion method with pattern-based approach
     */
    @Override
    public BaseNode doConvert(ParseTree tree, BaseNode parent) {
        if (tree == null) {
            return null;
        }
        
        Class<?> treeClass = tree.getClass();
        
        // Use specific converters for core syntax
        if (_coreConverters.contains(treeClass)) {
            return convertCoreNode(tree, parent);
        }
        
        // Try pattern-based conversion
        String patternType = detectExpressionPattern(tree);
        if (patternType != null && _patternHandlers.containsKey(patternType)) {
            ExpressionPatternHandler handler = _patternHandlers.get(patternType);
            return handler.convert(tree, this, parent);
        }
        
        // Fallback: create generic expression for unknown syntax
        return createGenericExpression(tree, parent);
    }
    
    /**
     * Convert core nodes using existing logic
     */
    private BaseNode convertCoreNode(ParseTree tree, BaseNode parent) {
        // Use the inherited converter map from ClosureConverter for core syntax
        ClosureNodeConverter converter = getConverter(tree);
        if (converter != null) {
            return converter.convert(tree, this);
        }
        
        // If no specific converter, fall back to generic
        return createGenericExpression(tree, parent);
    }
    
    /**
     * Detect the expression pattern type for a ParseTree
     */
    private String detectExpressionPattern(ParseTree tree) {
        String className = tree.getClass().getSimpleName();
        
        // Unary expressions
        if (className.contains("Unary") || className.equals("AwaitExpressionTree") || 
            className.equals("UpdateExpressionTree")) {
            return "UnaryExpression";
        }
        
        // Binary expressions
        if (className.contains("Binary") || className.contains("Operator")) {
            return "BinaryExpression";
        }
        
        // Call expressions
        if (className.contains("Call") || className.contains("New")) {
            return "CallExpression";
        }
        
        // Member expressions
        if (className.contains("Member") || className.contains("Property") || 
            className.contains("OptChain")) {
            return "MemberExpression";
        }
        
        // Literal expressions
        if (className.contains("Literal") || className.equals("IdentifierExpressionTree") ||
            className.equals("ThisExpressionTree") || className.equals("NullTree")) {
            return "LiteralExpression";
        }
        
        // Assignment expressions
        if (className.contains("Assignment")) {
            return "AssignmentExpression";
        }
        
        // Update expressions
        if (className.contains("Update")) {
            return "UpdateExpression";
        }
        
        // Conditional expressions
        if (className.contains("Conditional")) {
            return "ConditionalExpression";
        }
        
        return null; // Unknown pattern
    }
    
    /**
     * Create a GenericExpression for unknown or modern syntax
     */
    private GenericExpression createGenericExpression(ParseTree tree, BaseNode parent) {
        String expressionType = tree.getClass().getSimpleName().replace("Tree", "");
        GenericExpression generic = new GenericExpression(expressionType);
        
        if (parent != null) {
            generic.setParent(parent);
        }
        
        // Store original ParseTree for debugging
        generic.setOriginalParseTree(tree);
        
        // Extract properties and children using reflection
        extractPropertiesAndChildren(tree, generic);
        
        return generic;
    }
    
    /**
     * Extract properties and child nodes from ParseTree using reflection
     */
    private void extractPropertiesAndChildren(ParseTree tree, GenericExpression generic) {
        Class<?> treeClass = tree.getClass();
        
        // Extract public fields
        Field[] fields = treeClass.getFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                Object value = field.get(tree);
                String fieldName = field.getName();
                
                if (value == null) {
                    continue;
                }
                
                // Handle different types of values
                if (value instanceof ParseTree) {
                    // Convert child ParseTree to BaseNode
                    BaseNode childNode = doConvert((ParseTree) value, generic);
                    generic.setNamedChild(fieldName, childNode);
                } else if (value instanceof List) {
                    // Handle lists of ParseTree objects
                    List<?> list = (List<?>) value;
                    if (!list.isEmpty() && list.get(0) instanceof ParseTree) {
                        for (Object item : list) {
                            BaseNode childNode = doConvert((ParseTree) item, generic);
                            generic.addChild(childNode);
                        }
                    }
                } else if (isPrimitiveOrString(value)) {
                    // Store primitive properties
                    generic.setProperty(fieldName, value);
                }
            } catch (Exception e) {
                _logger.debug("Failed to extract field {}: {}", field.getName(), e.getMessage());
            }
        }
    }
    
    /**
     * Check if a value is a primitive type or string
     */
    private boolean isPrimitiveOrString(Object value) {
        return value instanceof String || value instanceof Number ||
               value instanceof Boolean || value instanceof Character ||
               value.getClass().isPrimitive() || value instanceof Enum;
    }

    // ========== PATTERN HANDLER IMPLEMENTATIONS ==========

    /**
     * Handler for unary expressions (await, typeof, delete, ++, --, etc.)
     */
    private static class UnaryExpressionHandler implements ExpressionPatternHandler {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter, BaseNode parent) {
            // Handle specific unary expressions
            if (tree instanceof AwaitExpressionTree) {
                return convertAwaitExpression((AwaitExpressionTree) tree, converter, parent);
            } else if (tree instanceof UnaryExpressionTree) {
                return convertUnaryExpression((UnaryExpressionTree) tree, converter, parent);
            } else if (tree instanceof UpdateExpressionTree) {
                return convertUpdateExpression((UpdateExpressionTree) tree, converter, parent);
            }

            // Fallback to generic expression
            return createGenericForHandler(tree, converter, parent, "UnaryExpression");
        }

        @Override
        public boolean canHandle(ParseTree tree) {
            return tree instanceof AwaitExpressionTree ||
                   tree instanceof UnaryExpressionTree ||
                   tree instanceof UpdateExpressionTree;
        }

        private BaseNode convertAwaitExpression(AwaitExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            AwaitExpression node = new AwaitExpression();
            if (parent != null) node.setParent(parent);
            node.setExpr(converter.doConvert(tree.expression, node));
            return node;
        }

        private BaseNode convertUnaryExpression(UnaryExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            Unary node = new Unary();
            if (parent != null) node.setParent(parent);
            node.setOperand(converter.doConvert(tree.operand, node));

            // Convert operator
            String operatorStr = tree.operator.toString();
            try {
                Operators operator = Operators.valueOf(operatorStr.toUpperCase());
                node.setOperator(operator);
            } catch (IllegalArgumentException e) {
                // Unknown operator, store as property in generic expression
                GenericExpression generic = new GenericExpression("UnaryExpression");
                generic.setProperty("operator", operatorStr);
                generic.setNamedChild("operand", converter.doConvert(tree.operand, generic));
                return generic;
            }

            return node;
        }

        private BaseNode convertUpdateExpression(UpdateExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            Unary node = new Unary();
            if (parent != null) node.setParent(parent);
            node.setOperand(converter.doConvert(tree.operand, node));

            // Check if the tree has isPostfix property (may not be available in all versions)
            boolean isPostfix = false;
            try {
                // Try to access isPostfix field via reflection if it exists
                java.lang.reflect.Field postfixField = tree.getClass().getField("isPostfix");
                isPostfix = postfixField.getBoolean(tree);
                node.setPostfix(isPostfix);
            } catch (Exception e) {
                // isPostfix field not available, assume prefix
                node.setPostfix(false);
            }

            // Convert operator (++ or --)
            String operatorStr = tree.operator.toString();
            if ("++".equals(operatorStr)) {
                node.setOperator(Operators.INC);
            } else if ("--".equals(operatorStr)) {
                node.setOperator(Operators.DEC);
            } else {
                // Unknown update operator
                GenericExpression generic = new GenericExpression("UpdateExpression");
                generic.setProperty("operator", operatorStr);
                generic.setProperty("isPostfix", isPostfix);
                generic.setNamedChild("operand", converter.doConvert(tree.operand, generic));
                return generic;
            }

            return node;
        }
    }

    /**
     * Handler for binary expressions (||, &&, ??, +, -, etc.)
     */
    private static class BinaryExpressionHandler implements ExpressionPatternHandler {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter, BaseNode parent) {
            if (tree instanceof BinaryOperatorTree) {
                return convertBinaryOperator((BinaryOperatorTree) tree, converter, parent);
            }

            return createGenericForHandler(tree, converter, parent, "BinaryExpression");
        }

        @Override
        public boolean canHandle(ParseTree tree) {
            return tree instanceof BinaryOperatorTree;
        }

        private BaseNode convertBinaryOperator(BinaryOperatorTree tree, ClosureASTConverter converter, BaseNode parent) {
            String operatorStr = tree.operator.type.toString();

            // Check for nullish coalescing or other modern operators
            if ("??".equals(operatorStr)) {
                GenericExpression generic = new GenericExpression("NullishCoalescing");
                if (parent != null) generic.setParent(parent);
                generic.setProperty("operator", operatorStr);
                generic.setNamedChild("left", converter.doConvert(tree.left, generic));
                generic.setNamedChild("right", converter.doConvert(tree.right, generic));
                return generic;
            }

            // Handle assignment operators
            if (operatorStr.contains("=") && !"==".equals(operatorStr) && !"===".equals(operatorStr) && !"!=".equals(operatorStr) && !"!==".equals(operatorStr)) {
                Assignment node = new Assignment();
                if (parent != null) node.setParent(parent);
                node.setLeft(converter.doConvert(tree.left, node));
                node.setRight(converter.doConvert(tree.right, node));

                try {
                    Operators operator = Operators.valueOf(operatorStr.toUpperCase().replace("=", "_ASSIGN"));
                    node.setOperator(operator);
                } catch (IllegalArgumentException e) {
                    // Unknown assignment operator
                    GenericExpression generic = new GenericExpression("AssignmentExpression");
                    generic.setProperty("operator", operatorStr);
                    generic.setNamedChild("left", converter.doConvert(tree.left, generic));
                    generic.setNamedChild("right", converter.doConvert(tree.right, generic));
                    return generic;
                }

                return node;
            }

            // Handle regular binary operators
            Infix node = new Infix();
            if (parent != null) node.setParent(parent);
            node.setLeft(converter.doConvert(tree.left, node));
            node.setRight(converter.doConvert(tree.right, node));

            try {
                Operators operator = Operators.valueOf(operatorStr.toUpperCase());
                node.setOperator(operator);
            } catch (IllegalArgumentException e) {
                // Unknown operator, create generic expression
                GenericExpression generic = new GenericExpression("BinaryExpression");
                generic.setProperty("operator", operatorStr);
                generic.setNamedChild("left", converter.doConvert(tree.left, generic));
                generic.setNamedChild("right", converter.doConvert(tree.right, generic));
                return generic;
            }

            return node;
        }
    }

    /**
     * Handler for call expressions (function calls, optional calls, new expressions, etc.)
     */
    private static class CallExpressionHandler implements ExpressionPatternHandler {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter, BaseNode parent) {
            if (tree instanceof CallExpressionTree) {
                return convertCallExpression((CallExpressionTree) tree, converter, parent);
            } else if (tree instanceof NewExpressionTree) {
                return convertNewExpression((NewExpressionTree) tree, converter, parent);
            }

            return createGenericForHandler(tree, converter, parent, "CallExpression");
        }

        @Override
        public boolean canHandle(ParseTree tree) {
            return tree instanceof CallExpressionTree || tree instanceof NewExpressionTree;
        }

        private BaseNode convertCallExpression(CallExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            FunctionCall node = new FunctionCall();
            if (parent != null) node.setParent(parent);
            node.setTarget(converter.doConvert(tree.operand, node));

            // Convert arguments
            for (ParseTree arg : tree.arguments.arguments) {
                BaseNode argNode = converter.doConvert(arg, node);
                node.addArgument(argNode);
            }

            return node;
        }

        private BaseNode convertNewExpression(NewExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            NewExpression node = new NewExpression();
            if (parent != null) node.setParent(parent);
            node.setTarget(converter.doConvert(tree.operand, node));

            // Convert arguments if present
            if (tree.arguments != null) {
                for (ParseTree arg : tree.arguments.arguments) {
                    BaseNode argNode = converter.doConvert(arg, node);
                    node.addArgument(argNode);
                }
            }

            return node;
        }
    }

    /**
     * Handler for member expressions (property access, optional chaining, etc.)
     */
    private static class MemberExpressionHandler implements ExpressionPatternHandler {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter, BaseNode parent) {
            if (tree instanceof MemberExpressionTree) {
                return convertMemberExpression((MemberExpressionTree) tree, converter, parent);
            } else if (tree instanceof MemberLookupExpressionTree) {
                return convertMemberLookupExpression((MemberLookupExpressionTree) tree, converter, parent);
            } else if (tree instanceof OptionalMemberExpressionTree) {
                return convertOptionalMemberExpression((OptionalMemberExpressionTree) tree, converter, parent);
            } else if (tree instanceof OptionalMemberLookupExpressionTree) {
                return convertOptionalMemberLookupExpression((OptionalMemberLookupExpressionTree) tree, converter, parent);
            }

            return createGenericForHandler(tree, converter, parent, "MemberExpression");
        }

        @Override
        public boolean canHandle(ParseTree tree) {
            return tree instanceof MemberExpressionTree ||
                   tree instanceof MemberLookupExpressionTree ||
                   tree instanceof OptionalMemberExpressionTree ||
                   tree instanceof OptionalMemberLookupExpressionTree;
        }

        private BaseNode convertMemberExpression(MemberExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            PropertyGet node = new PropertyGet();
            if (parent != null) node.setParent(parent);
            node.setLeft(converter.doConvert(tree.operand, node));

            // Convert memberName (IdentifierToken) to a Name node
            Name propertyName = new Name();
            propertyName.setValue(tree.memberName.value);
            propertyName.setParent(node);
            node.setRight(propertyName);

            return node;
        }

        private BaseNode convertMemberLookupExpression(MemberLookupExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            ElementGet node = new ElementGet();
            if (parent != null) node.setParent(parent);
            node.setTarget(converter.doConvert(tree.operand, node));
            node.setElement(converter.doConvert(tree.memberExpression, node));
            return node;
        }

        private BaseNode convertOptionalMemberExpression(OptionalMemberExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            // Create generic expression for optional chaining
            GenericExpression generic = new GenericExpression("OptionalMemberExpression");
            if (parent != null) generic.setParent(parent);
            generic.setNamedChild("operand", converter.doConvert(tree.operand, generic));

            // Convert memberName (IdentifierToken) to a Name node
            Name memberName = new Name();
            memberName.setValue(tree.memberName.value);
            memberName.setParent(generic);
            generic.setNamedChild("memberName", memberName);

            return generic;
        }

        private BaseNode convertOptionalMemberLookupExpression(OptionalMemberLookupExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            // Create generic expression for optional chaining lookup
            GenericExpression generic = new GenericExpression("OptionalMemberLookupExpression");
            if (parent != null) generic.setParent(parent);
            generic.setNamedChild("operand", converter.doConvert(tree.operand, generic));
            generic.setNamedChild("memberExpression", converter.doConvert(tree.memberExpression, generic));
            return generic;
        }
    }

    /**
     * Handler for literal expressions (strings, numbers, booleans, identifiers, etc.)
     */
    private static class LiteralExpressionHandler implements ExpressionPatternHandler {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter, BaseNode parent) {
            if (tree instanceof LiteralExpressionTree) {
                return convertLiteralExpression((LiteralExpressionTree) tree, converter, parent);
            } else if (tree instanceof IdentifierExpressionTree) {
                return convertIdentifierExpression((IdentifierExpressionTree) tree, converter, parent);
            } else if (tree instanceof ThisExpressionTree) {
                return convertThisExpression((ThisExpressionTree) tree, converter, parent);
            } else if (tree instanceof NullTree) {
                return convertNullExpression((NullTree) tree, converter, parent);
            }

            return createGenericForHandler(tree, converter, parent, "LiteralExpression");
        }

        @Override
        public boolean canHandle(ParseTree tree) {
            return tree instanceof LiteralExpressionTree ||
                   tree instanceof IdentifierExpressionTree ||
                   tree instanceof ThisExpressionTree ||
                   tree instanceof NullTree;
        }

        private BaseNode convertLiteralExpression(LiteralExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            String value = tree.literalToken.toString();

            if (tree.literalToken.type.toString().equals("STRING")) {
                StringLiteral node = new StringLiteral();
                if (parent != null) node.setParent(parent);
                node.setValue(value.substring(1, value.length() - 1)); // Remove quotes
                node.setQuoteCharacter(value.charAt(0));
                return node;
            } else if (tree.literalToken.type.toString().equals("NUMBER")) {
                NumberLiteral node = new NumberLiteral();
                if (parent != null) node.setParent(parent);
                try {
                    node.setValue(Double.parseDouble(value));
                } catch (NumberFormatException e) {
                    node.setValue(0.0);
                }
                return node;
            } else if ("TRUE".equals(value) || "FALSE".equals(value)) {
                KeywordLiteral node = new KeywordLiteral();
                if (parent != null) node.setParent(parent);
                node.setValue(value.toLowerCase());
                return node;
            }

            // Unknown literal type
            GenericExpression generic = new GenericExpression("LiteralExpression");
            if (parent != null) generic.setParent(parent);
            generic.setProperty("value", value);
            generic.setProperty("type", tree.literalToken.type.toString());
            return generic;
        }

        private BaseNode convertIdentifierExpression(IdentifierExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            Name node = new Name();
            if (parent != null) node.setParent(parent);
            node.setValue(tree.identifierToken.value);
            return node;
        }

        private BaseNode convertThisExpression(ThisExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            KeywordLiteral node = new KeywordLiteral();
            if (parent != null) node.setParent(parent);
            node.setValue("this");
            return node;
        }

        private BaseNode convertNullExpression(NullTree tree, ClosureASTConverter converter, BaseNode parent) {
            EmptyExpression node = new EmptyExpression();
            if (parent != null) node.setParent(parent);
            return node;
        }
    }

    /**
     * Handler for assignment expressions
     */
    private static class AssignmentExpressionHandler implements ExpressionPatternHandler {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter, BaseNode parent) {
            // Assignment expressions are typically handled by BinaryExpressionHandler
            // This is a placeholder for specific assignment logic if needed
            return createGenericForHandler(tree, converter, parent, "AssignmentExpression");
        }

        @Override
        public boolean canHandle(ParseTree tree) {
            return false; // Assignments are handled by BinaryExpressionHandler
        }
    }

    /**
     * Handler for update expressions (++, --)
     */
    private static class UpdateExpressionHandler implements ExpressionPatternHandler {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter, BaseNode parent) {
            // Update expressions are handled by UnaryExpressionHandler
            return createGenericForHandler(tree, converter, parent, "UpdateExpression");
        }

        @Override
        public boolean canHandle(ParseTree tree) {
            return false; // Updates are handled by UnaryExpressionHandler
        }
    }

    /**
     * Handler for conditional expressions (ternary operator)
     */
    private static class ConditionalExpressionHandler implements ExpressionPatternHandler {
        @Override
        public BaseNode convert(ParseTree tree, ClosureASTConverter converter, BaseNode parent) {
            if (tree instanceof ConditionalExpressionTree) {
                return convertConditionalExpression((ConditionalExpressionTree) tree, converter, parent);
            }

            return createGenericForHandler(tree, converter, parent, "ConditionalExpression");
        }

        @Override
        public boolean canHandle(ParseTree tree) {
            return tree instanceof ConditionalExpressionTree;
        }

        private BaseNode convertConditionalExpression(ConditionalExpressionTree tree, ClosureASTConverter converter, BaseNode parent) {
            ConditionalExpression node = new ConditionalExpression();
            if (parent != null) node.setParent(parent);
            node.setTest(converter.doConvert(tree.condition, node));
            node.setTrue(converter.doConvert(tree.left, node));
            node.setFalse(converter.doConvert(tree.right, node));
            return node;
        }
    }

    /**
     * Helper method to create generic expressions for pattern handlers
     */
    private static GenericExpression createGenericForHandler(ParseTree tree, ClosureASTConverter converter, BaseNode parent, String patternType) {
        String expressionType = tree.getClass().getSimpleName().replace("Tree", "");
        GenericExpression generic = new GenericExpression(expressionType);

        if (parent != null) {
            generic.setParent(parent);
        }

        // Store original ParseTree for debugging
        generic.setOriginalParseTree(tree);

        // Extract properties and children using reflection
        if (converter instanceof GenericClosureConverter) {
            ((GenericClosureConverter) converter).extractPropertiesAndChildren(tree, generic);
        }

        return generic;
    }

    // ========== CORE CONVERTER METHODS (Required by ClosureASTConverter) ==========

    @Override
    public BaseNode convert(ProgramTree tree) {
        RootNode root = new RootNode();
        for (ParseTree statement : tree.sourceElements) {
            BaseNode node = doConvert(statement, root);
            if (node != null) {
                root.addElement(node);
            }
        }
        return root;
    }

    @Override
    public BaseNode convert(BlockTree tree) {
        Block block = new Block();
        for (ParseTree statement : tree.statements) {
            BaseNode node = doConvert(statement, block);
            if (node != null) {
                block.addElement(node);
            }
        }
        return block;
    }

    @Override
    public BaseNode convert(ExpressionStatementTree tree) {
        ExpressionStatement stmt = new ExpressionStatement();
        stmt.setExpression(doConvert(tree.expression, stmt));
        return stmt;
    }

    @Override
    public BaseNode convert(VariableDeclarationTree tree) {
        VariableInitializer init = new VariableInitializer();
        init.setTarget(doConvert(tree.lvalue, init));
        if (tree.initializer != null) {
            init.setInitializer(doConvert(tree.initializer, init));
        }
        return init;
    }

    @Override
    public BaseNode convert(FunctionDeclarationTree tree) {
        FunctionNode func = new FunctionNode();

        // Set function name
        if (tree.name != null) {
            Name name = new Name();
            name.setValue(tree.name.value);
            func.setName(name);
        }

        // Add parameters
        if (tree.formalParameterList != null) {
            for (ParseTree param : tree.formalParameterList.parameters) {
                BaseNode paramNode = doConvert(param, func);
                if (paramNode instanceof Name) {
                    func.addParam(paramNode);
                }
            }
        }

        // Set function body
        func.setBody(doConvert(tree.functionBody, func));

        // Handle modern function features
        if (tree.isAsync) {
            func.setAsync(true);
        }
        if (tree.isGenerator) {
            func.setGenerator(true);
        }

        return func;
    }

    @Override
    public BaseNode convert(ReturnStatementTree tree) {
        ReturnStatement ret = new ReturnStatement();
        if (tree.expression != null) {
            ret.setReturnValue(doConvert(tree.expression, ret));
        }
        return ret;
    }

    @Override
    public BaseNode convert(IfStatementTree tree) {
        IfStatement ifStmt = new IfStatement();
        ifStmt.setCondition(doConvert(tree.condition, ifStmt));
        ifStmt.setThen(doConvert(tree.ifClause, ifStmt));
        if (tree.elseClause != null) {
            ifStmt.setElse(doConvert(tree.elseClause, ifStmt));
        }
        return ifStmt;
    }

    @Override
    public BaseNode convert(ForStatementTree tree) {
        ForLoop forLoop = new ForLoop();
        if (tree.initializer != null) {
            forLoop.setInitializer(doConvert(tree.initializer, forLoop));
        }
        if (tree.condition != null) {
            forLoop.setCondition(doConvert(tree.condition, forLoop));
        }
        if (tree.increment != null) {
            forLoop.setIncrement(doConvert(tree.increment, forLoop));
        }
        forLoop.setBody(doConvert(tree.body, forLoop));
        return forLoop;
    }

    @Override
    public BaseNode convert(WhileStatementTree tree) {
        WhileLoop whileLoop = new WhileLoop();
        whileLoop.setCondition(doConvert(tree.condition, whileLoop));
        whileLoop.setBody(doConvert(tree.body, whileLoop));
        return whileLoop;
    }

    @Override
    public BaseNode convert(ObjectLiteralExpressionTree tree) {
        ObjectLiteral obj = new ObjectLiteral();
        for (ParseTree prop : tree.propertyNameAndValues) {
            BaseNode propNode = doConvert(prop, obj);
            if (propNode instanceof ObjectProperty) {
                obj.addElement((ObjectProperty) propNode);
            }
        }
        return obj;
    }

    @Override
    public BaseNode convert(ArrayLiteralExpressionTree tree) {
        ArrayLiteral array = new ArrayLiteral();
        for (ParseTree element : tree.elements) {
            BaseNode elementNode = doConvert(element, array);
            array.addElement(elementNode);
        }
        return array;
    }

    @Override
    public BaseNode convert(PropertyNameAssignmentTree tree) {
        ObjectProperty prop = new ObjectProperty();
        // tree.name is a Token, not a ParseTree, so we need to create a node from it
        Name propertyName = new Name();
        propertyName.setValue(tree.name.toString());
        propertyName.setParent(prop);
        prop.setLeft(propertyName);
        prop.setRight(doConvert(tree.value, prop));
        return prop;
    }

    // ========== PATTERN-BASED CONVERTER METHODS ==========
    // These methods use the pattern-based approach for modern JavaScript syntax

    @Override
    public BaseNode convert(AwaitExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by UnaryExpressionHandler
    }

    @Override
    public BaseNode convert(BinaryOperatorTree tree) {
        return doConvert(tree, null); // Will be handled by BinaryExpressionHandler
    }

    @Override
    public BaseNode convert(CallExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by CallExpressionHandler
    }

    @Override
    public BaseNode convert(NewExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by CallExpressionHandler
    }

    @Override
    public BaseNode convert(MemberExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by MemberExpressionHandler
    }

    @Override
    public BaseNode convert(MemberLookupExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by MemberExpressionHandler
    }

    @Override
    public BaseNode convert(OptionalMemberExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by MemberExpressionHandler
    }

    @Override
    public BaseNode convert(OptionalMemberLookupExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by MemberExpressionHandler
    }

    @Override
    public BaseNode convert(LiteralExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by LiteralExpressionHandler
    }

    @Override
    public BaseNode convert(IdentifierExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by LiteralExpressionHandler
    }

    @Override
    public BaseNode convert(ThisExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by LiteralExpressionHandler
    }

    @Override
    public BaseNode convert(NullTree tree) {
        return doConvert(tree, null); // Will be handled by LiteralExpressionHandler
    }

    @Override
    public BaseNode convert(ConditionalExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by ConditionalExpressionHandler
    }

    @Override
    public BaseNode convert(UnaryExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by UnaryExpressionHandler
    }

    @Override
    public BaseNode convert(UpdateExpressionTree tree) {
        return doConvert(tree, null); // Will be handled by UnaryExpressionHandler
    }

    // ========== STUB METHODS FOR REMAINING INTERFACE REQUIREMENTS ==========
    // These create GenericExpression nodes for syntax not yet specifically handled

    @Override
    public BaseNode convert(ArgumentListTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ArrayPatternTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(BreakStatementTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(CaseClauseTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(CatchTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ClassDeclarationTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(CommaExpressionTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(com.google.javascript.jscomp.parsing.parser.trees.Comment tree) {
        // Comment is not a ParseTree, so we create a custom Comment node
        com.sencha.tools.compiler.ast.js.BlockComment comment = new com.sencha.tools.compiler.ast.js.BlockComment();
        comment.setValue(tree.value);
        return comment;
    }

    @Override
    public BaseNode convert(ComprehensionForTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ComprehensionIfTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ComprehensionTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ComputedPropertyDefinitionTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ComputedPropertyGetterTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ComputedPropertyMethodTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ComputedPropertySetterTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(ContinueStatementTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(DebuggerStatementTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(DefaultClauseTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(DefaultParameterTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(DoWhileStatementTree tree) {
        return createGenericExpression(tree, null);
    }

    @Override
    public BaseNode convert(DynamicImportTree tree) {
        return createGenericExpression(tree, null);
    }

    // Add remaining required methods as stubs - only those not already defined
    @Override public BaseNode convert(EmptyStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ExportDeclarationTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ExportSpecifierTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(FieldDeclarationTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(FinallyTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ForAwaitOfStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ForInStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(FormalParameterListTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ForOfStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(GetAccessorTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ImportDeclarationTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ImportMetaExpressionTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ImportSpecifierTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(IterRestTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(IterSpreadTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(LabelledStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(MissingPrimaryExpressionTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(NewTargetExpressionTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ObjectPatternTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ObjectRestTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ObjectSpreadTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ParenExpressionTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(SetAccessorTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(SuperExpressionTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(SwitchStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(TemplateLiteralExpressionTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(TemplateLiteralPortionTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(TemplateSubstitutionTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(ThrowStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(TryStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(VariableDeclarationListTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(VariableStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(WithStatementTree tree) { return createGenericExpression(tree, null); }
    @Override public BaseNode convert(YieldExpressionTree tree) { return createGenericExpression(tree, null); }
}
