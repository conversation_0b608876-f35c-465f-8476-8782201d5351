<project name="x-sass-impl">
    
    <!--
    Uses the compiler to generate the top-level scss file for the app
    by using the current set of js files to determine the Components
    used by the app, then including the corresponding scss files into the 
    app's style
    -->
    <target name="-compile-sass" depends="-init-app-js-files">
        <local name="app.out.scss.tmp"/>
        <local name="sass.name.filter"/>
		<property name="app.out.scss.tmp" value="${app.out.scss}.tmp"/>
        <x-normalize-path 
            path="${build.out.resources.dir}" 
            property="image.search.path"/>
        
        <if>
            <or>
                <x-is-true value="${app.has.uses}" />
                <x-is-true value="${build.include.all.scss}"/>
            </or>
            <then>
                <property name="sass.name.filter">
                    include
                        -all
                </property>
            </then>
        </if>
        <property name="sass.name.filter">
            restore
                page
        </property>

        <local name="var.begin"/>
        <local name="var.end"/>
        <local name="resource.map.base"/>

        <condition property="var.begin" value="dynamic(">
            <isset property="framework.isV6"/>
        </condition>
        <condition property="var.end" value=")">
            <isset property="framework.isV6"/>
        </condition>

        <property name="var.begin" value=""/>
        <property name="var.end" value="!default"/>

        <condition property="resource.map.base" value="${css.output.rel.path}">
            <x-is-true value="${app.sass.dynamic}"/>
        </condition>
        <property name="resource.map.base" value=""/>

        <if>
            <x-is-true value="${app.sass.dynamic}"/>
            <then>
                <x-get-relative-path
                        from="${build.out.css.dir}"
                        to="${build.out.base.path}"
                        property="css.output.rel.path"/>
            </then>
        </if>

        <property name="css.output.rel.path" value=""/>

        <x-compile refid="${compiler.ref.id}">
            <![CDATA[
                ${sass.name.filter}
                and
                #only set variables for used classes eg. $include-class-name
                sass
                    +class-name-vars
                    -variable=$app-name: ${var.begin} '${app.name}' ${var.end}
                    -variable=$image-search-path:'${image.search.path}'
                    -variable=$theme-name: ${var.begin} '${theme.name}' ${var.end}
                    -resourceMapBase=${css.output.rel.path}
                    -output=${app.out.scss.tmp}
                    -forward=${app.sass.dynamic}
                and
                include
                    -all
                and
                # include etc and vars from all classes
                sass
                    +etc
                    +vars
                    +append
                    -output=${app.out.scss.tmp}
                    -forward=${app.sass.dynamic}
                and
                ${sass.name.filter}
                and
                #only include rules from used classes
                sass
                    +rules
                    +append
                    -output=${app.out.scss.tmp}
                and
                sass
                    +ruby
                    -sassCacheDir=${compass.cache.dir}
                    -output=${app.out.ruby}
            ]]>
        </x-compile>
		
		<if>
			<not>
				<filesmatch file1="${app.out.scss.tmp}" file2="${app.out.scss}"/>
			</not>
			<then>
				<copy file="${app.out.scss.tmp}" tofile="${app.out.scss}" overwrite="true"/>
			</then>
		</if>
    
        <if>
            <equals arg1="development" arg2="${build.environment}"/>
            <then>
                <!--
                app.out.css.path is relative to the app output index.html file
                -->
                <x-get-relative-path
                    from="${app.dir}"
                    to="${app.out.css}"
                    property="app.out.css.path"
                />
      
<!--update the application's bootstrap.css file to point to the build output-->
<x-file-write file="${build.bootstrap.css.path}">
<![CDATA[
/*
 * This file is generated by Sencha Cmd and should NOT be edited. It redirects
 * to the most recently built CSS file for the application to allow index.html
 * in the development directory to load properly (i.e., "dev mode").
 */
@import '${app.out.css.path}';
]]>
</x-file-write>
            </then>
        </if>
    </target>

    <!--
    This macrodef is used for post-processing Ext JS 4.2+ style theme css files
    and will split based on selector thresholds, as well as run the css preprocessor
    and compressor
    -->
    <macrodef name="x-compress-css-files">
        <attribute name="dir"/>
        <attribute name="prefix"/>
        <attribute name="outprefix"/>
        <attribute name="compress"/>
        <attribute name="preprocess"/>
        <sequential>
            <x-split-css file="@{dir}/@{prefix}.css"
                         outdir="${build.resources.dir}"
                         limit="${build.css.selector.limit}"
                         compilerRefId="${compiler.ref.id}"/>

            <for param="cssfile">
                <fileset dir="@{dir}" includes="@{prefix}*.css"/>
                <sequential>
                    <local name="css.output.name"/>
                    <local name="pattern"/>
                    <property name="pattern" value="(.*?)(@{prefix})(_\d{1,2})*\.css"/>
                    <propertyregex property="css.output.name"
                                   input="@{cssfile}"
                                   regexp="${pattern}"
                                   select="\1@{outprefix}\3.css"
                                   override="true"/>
                    <if>
                        <equals arg1="@{preprocess}" arg2="true"/>
                        <then>
                            <x-echo>Preprocessing @{cssfile} to ${css.output.name}</x-echo>
                            <x-css-preprocess 
                                file="@{cssfile}" 
                                tofile="${css.output.name}" 
                                options="${build.css.preprocessor.opts}"/>
                        </then>
                    </if>
                    <if>
                        <equals arg1="@{compress}" arg2="true"/>
                        <then>
                            <x-echo>Compressing @{cssfile} to ${css.output.name}</x-echo>
                            <x-compress-css srcfile="@{cssfile}"
                                            outfile="${css.output.name}"/>
                        </then>
                    </if>
                </sequential>
            </for>
        </sequential>
    </macrodef>

    <!--
    This target builds Ext JS 4.2+ style themes, first generating the top-level
    scss file, then running compass with the css, sass, and config options set
    -->
    <target name="-compass-compile-theme-package" depends="-load-sass-page">
        <if>
            <x-is-true value="${skip.sass.rebuild}"/>
            <then>
                <x-run-if-true value="${enable.ext42.themes}">
                    <x-ant-call target="-compile-sass"/>
                </x-run-if-true>
            </then>
            <else>
                <x-run-if-true value="${enable.ext42.themes}">
                    <local name="compress.uptodate"/>

                    <x-ant-call target="-compile-sass"/>

                    <if>
                        <x-is-true value="${app.sass.fashion}"/>
                        <then>
                            <if>
                                <x-is-true value="${app.sass.rhino}"/>
                                <then>
                                    <x-fashion-compile
                                            file="${app.out.scss}"
                                            toFile="${app.out.css}"/>
                                </then>
                                <else>
                                    <x-fashion-live-update input="${app.out.scss}"
                                                           output="${app.out.css}"
                                                           refId="app.web.server"
                                                           split="${build.css.selector.limit}"
                                                           compress="${build.css.compress}"
                                                           compilerRefId="${compiler.ref.id}"/>

                                    <x-compile refid="${compiler.ref.id}">
                                        <![CDATA[
                                microload
                                    -operation=manifest
                                    -fashion=false
                                    -tpl=${build.microloader.json.tpl.standalone}
                                    -out=${build.out.json.path}
                                    -resourcePath=${build.out.base.path}
                                    -basePath=${build.out.metadata.dir}
                                ]]>
                                    </x-compile>

                                    <x-sencha-command>
                                        fashion
                                        -config=${build.out.json.path}
                                        -compress=${build.css.compress}
                                        -split=${build.css.selector.limit}
                                        -saveFile=${app.dir}/${app.sass.generated.var}
                                        -slicer=false
                                        ${app.out.scss}
                                        ${app.out.css}
                                    </x-sencha-command>

                                    <x-update-css-array input="${app.out.css}"
                                                        compilerRefId="${compiler.ref.id}"/>

                                    <if>
                                        <and>
                                            <available file="${build.out.css.dir}/css-vars.js"/>
                                        </and>
                                        <then>

                                            <if>
                                                <not>
                                                    <equals arg1="${build.compression}" arg2=""/>
                                                </not>
                                                <then>
                                                    <x-compress-js srcFile="${build.out.css.dir}/css-vars.js"
                                                                   outFile="${build.out.css.dir}/css-vars.js"/>
                                                </then>
                                            </if>

                                            <if>
                                                <not>
                                                    <equals arg1="${build.environment}" arg2="development"/>
                                                </not>
                                                <then>
                                                    <concat destfile="${build.out.js.path}" append="true">
                                                        <fileset dir="${build.out.css.dir}">
                                                            <include name="css-vars.js"/>
                                                        </fileset>
                                                    </concat>
                                                </then>
                                            </if>

                                        </then>
                                    </if>
                                </else>
                            </if>
                        </then>
                        <else>
                            <x-compass-compile
                                    rubyPath="${build.ruby.path}"
                                    dir="${compass.working.dir}"
                                    trace="${compass.compile.trace}"
                                    boring="${compass.compile.boring}"
                                    force="${compass.compile.force}"
                                    sassdir="${compass.sass.dir}"
                                    cssdir="${compass.css.dir}"
                                    config="${compass.config.file}"/>

                            <uptodate property="compress.uptodate"
                                      value="true"
                                      srcfile="${app.out.scss}.tmp"
                                      targetfile="${app.out.css}"/>

                            <if>
                                <x-is-true value="${compress.uptodate}"/>
                                <!--<x-is-true value="true"/>-->
                                <then>
                                    <x-compress-css-files dir="${build.out.css.dir}"
                                                          prefix="${app.out.base.debug}"
                                                          outprefix="${app.out.base}"
                                                          preprocess="${build.css.preprocess}"
                                                          compress="${build.css.compress}"/>
                                </then>
                            </if>
                        </else>
                    </if>
                </x-run-if-true>
            </else>
        </if>
        
    </target>

    <!--
    This is a legacy macrodef to support building Ext JS 4.1 themes, which have been
    deprecated in favor of Ext JS 4.2 theme packages
    -->
    <macrodef name="x-build-sass">
        <attribute name="theme"/>
        <sequential>
            <local name="sass.name"/>
            <local name="use.shell"/>

            <!--
            convert abspath to just the leaf path name
            -->
            <basename property="sass.name" file="@{theme}"/>
            <local name="sass.base.name"/>
            <property name="sass.base.name" value="${sass.name}"/>
            
            <echo>Compiling sass directory : @{theme}/sass</echo>
            <x-compass-compile
                    rubyPath="${build.ruby.path}"
                    boring="${compass.compile.boring}"
                    force="${compass.compile.force}"
                    trace="${compass.compile.trace}"
                    dir="@{theme}/sass"/>

            <x-compress-css srcfile="${app.dir}/resources/${sass.base.name}/*.css"
                            outdir="${app.dir}/resources/${sass.base.name}"/>
        </sequential>
    </macrodef>
        
    <!--
    This target builds Ext JS 4.1 style themes, iterating over each directory
    under the specified ${app.theme.dir} directory and compiling the sass 
    located there
    -->
    <target name="-compass-compile-theme-folders">
        <x-run-if-true value="${enable.ext41.themes}">
            <!-- run sass compilation over the various themes -->
            <for param="sass">
                <dirset dir="${app.theme.dir}" includes="*"/>
                <sequential>
                    <x-build-sass theme="@{sass}"/>
                </sequential>
            </for>
        </x-run-if-true>
    </target>

    <!--
    This target builds Touch style themes, by running compass
    over the directory containing the manually maintined scss files
    -->
    <target name="-compass-compile-sass-dir">
        <x-run-if-true value="${enable.touch.themes}">
            <x-compass-compile
                    rubyPath="${build.ruby.path}"
                    trace="${compass.compile.trace}"
                    boring="${compass.compile.boring}"
                    force="${compass.compile.force}"
                    dir="${compass.sass.dir}"/>
        </x-run-if-true>
    </target>

    <!--
    This is a summation target triggering the three different supported
    sass modes (ext 41, ext 42+, and touch).            
    -->
    <target name="-compass-compile"
            depends="-compass-compile-theme-package,
                     -compass-compile-theme-folders,
                     -compass-compile-sass-dir"/>

    <!--
    Build SASS
    -->
    <target name="-before-sass"/>
    <target name="-sass" depends="-compass-compile"/>
    <target name="-after-sass"/>
</project>
