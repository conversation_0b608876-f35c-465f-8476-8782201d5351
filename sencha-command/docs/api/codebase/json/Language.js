/**
 * @class Cmd.codebase.json.Language
 * The `language` configuration object can be placed in `'app.json'` (or `'package.json'` as
 * appropriate) or in  `'workspace.json'` to serve as defaults for apps and packages.
 *
 * When setting language `input` and `output` levels, Sencha Cmd will transpile code when
 * the `input` language is newer then the `output` language. For example, when `input` is
 * set to `ES6` and `output` is set to `ES5`. Sencha Cmd uses the
 * [Google Closure Compiler](https://github.com/google/closure-compiler) to read and transpile
 * such code.
 *
 * Some common use cases follow.
 *
 * ## Enable ES6 and Transpiler
 *
 * This is the default setting so no customization is needed, but the following is the
 * explicit equivalent:
 *
 *      "language": {
 *          "output": "ES5"
 *      }
 *
 * ## Disable ES6 and Transpiler
 *
 * The following will reject all ES6+ syntax to ensure no transpiling is needed:
 *
 *      "language": {
 *          "input": "ES5"
 *      }
 *
 * ## Enable ES6 and Disable Transpiler
 *
 * If your target environment supports ES6 natively, the transpiler can and should be
 * disabled for size and performance benefits. The following will accept ES6 syntax and
 * disable the transpiler.
 *
 *      "language": {
 *          "input": "ES6",
 *          "output": "ES6"
 *      }
 *
 * ## Enable ES6+ and Disable Transpiler
 *
 * If your target environment supports ES6 and beyond natively, the transpiler can and
 * should be disabled for size and performance benefits. The following will accept ES6+
 * syntax and disable the transpiler.
 *
 *      "language": {
 *          "output": "ANY"
 *      }
 */

/**
 * @cfg {"ES5"/"ES6"/"ES7"/"ES8"} input
 * Specifies the input language level. By default, Sencha Cmd uses the most advanced parser
 * settings so as to accept all ES6 and most ES8 syntax.
 *
 * By setting this to `ES5`, Cmd uses the Rhino JavaScript compiler to parse `.js` files
 * since it only accepts ES5 syntax. In other words, no ES6 or higher language syntax will
 * compile. This is helpful if the goal is to run on all browsers without using the transpiler.
 *
 * Setting to `ES6` enables [Google Closure Compiler](https://github.com/google/closure-compiler)
 * for parsing. Sencha Cmd uses the `output` config to determine if this code needs to be
 * transpiled down to ES5.
 */

/**
 * @cfg {"ES5"/"ES6"/"ES7"/"ES8"/"ANY"} [output="ES5"]
 * Specifies the output language level. If this value is less then the `input` language level
 * then Sencha Cmd will enable the transpiler. The value of `ANY` indicates that the transpiler
 * should be disabled regardless of the `input` language level.
 *
 * For versions ES6 or higher of output language, the currently bundled version of
 * closure compiler will be disable the transpiler (For example, `async` and `await` will 
 * not be transformed when output version is `ES6`).
 * 
 * For clarity:
 *
 *      input       output      transpiler
 *      ------      -------     -----------
 *      ES5         ES5         No
 *      ES5         ES6+        No
 *      ES5         ANY         No
 *
 *      ES6         ES5         Yes
 *      ES6         ES6+        No
 *      ES6         ANY         No
 *
 *      (default)   ES5         Yes
 *      (default)   ES6+        Yes
 *      (default)   ANY         No
 */
